{"project_info": {"project_number": "11021412400", "project_id": "inkstone-path", "storage_bucket": "inkstone-path.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:11021412400:android:601534d2055bed8664a5f5", "android_client_info": {"package_name": "com.tfkcolin.ministep"}}, "oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB_zQY3A-QBES54fnap99u37jPYWpfvhzA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:11021412400:android:5d54435732e7893764a5f5", "android_client_info": {"package_name": "com.tfkcolin.practice"}}, "oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB_zQY3A-QBES54fnap99u37jPYWpfvhzA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:11021412400:android:3a9c34a5a36c710164a5f5", "android_client_info": {"package_name": "com.tfkcolin.translate"}}, "oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB_zQY3A-QBES54fnap99u37jPYWpfvhzA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}