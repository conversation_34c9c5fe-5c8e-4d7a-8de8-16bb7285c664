{"formatVersion": 1, "database": {"version": 1, "identityHash": "07be9b62ce02134b7124d9df2ed65440", "entities": [{"tableName": "translations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `originalText` TEXT NOT NULL, `translatedText` TEXT NOT NULL, `originalTextPronunciations` TEXT, `sourceLanguage` TEXT NOT NULL, `targetLanguage` TEXT NOT NULL, `timestamp` TEXT NOT NULL, `source` TEXT NOT NULL, `isStarred` INTEGER NOT NULL, `words` TEXT NOT NULL, `expressions` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalText", "columnName": "originalText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "translatedText", "columnName": "translatedText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalTextPronunciations", "columnName": "originalTextPronunciations", "affinity": "TEXT"}, {"fieldPath": "sourceLanguage", "columnName": "sourceLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": true}, {"fieldPath": "source", "columnName": "source", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isStarred", "columnName": "isStarred", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "words", "columnName": "words", "affinity": "TEXT", "notNull": true}, {"fieldPath": "expressions", "columnName": "expressions", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "word_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`normalizedWord` TEXT NOT NULL, `encounterCount` INTEGER NOT NULL, `lastEncountered` INTEGER NOT NULL, PRIMARY KEY(`normalizedWord`))", "fields": [{"fieldPath": "normalizedWord", "columnName": "normalizedWord", "affinity": "TEXT", "notNull": true}, {"fieldPath": "encounterCount", "columnName": "encounterCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastEncountered", "columnName": "lastEncountered", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["normalizedWord"]}}, {"tableName": "expression_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`normalizedExpression` TEXT NOT NULL, `encounterCount` INTEGER NOT NULL, `lastEncountered` INTEGER NOT NULL, PRIMARY KEY(`normalizedExpression`))", "fields": [{"fieldPath": "normalizedExpression", "columnName": "normalizedExpression", "affinity": "TEXT", "notNull": true}, {"fieldPath": "encounterCount", "columnName": "encounterCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastEncountered", "columnName": "lastEncountered", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["normalizedExpression"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '07be9b62ce02134b7124d9df2ed65440')"]}}