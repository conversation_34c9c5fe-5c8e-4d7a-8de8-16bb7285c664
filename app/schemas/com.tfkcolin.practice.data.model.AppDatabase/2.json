{"formatVersion": 1, "database": {"version": 2, "identityHash": "abf38dc1a1dfddf4b9a5bcb9b661265d", "entities": [{"tableName": "translations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `originalText` TEXT NOT NULL, `translatedText` TEXT NOT NULL, `originalTextPronunciations` TEXT, `sourceLanguage` TEXT NOT NULL, `targetLanguage` TEXT NOT NULL, `timestamp` TEXT NOT NULL, `source` TEXT NOT NULL, `isStarred` INTEGER NOT NULL, `words` TEXT NOT NULL, `expressions` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalText", "columnName": "originalText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "translatedText", "columnName": "translatedText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalTextPronunciations", "columnName": "originalTextPronunciations", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sourceLanguage", "columnName": "sourceLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": true}, {"fieldPath": "source", "columnName": "source", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isStarred", "columnName": "isStarred", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "words", "columnName": "words", "affinity": "TEXT", "notNull": true}, {"fieldPath": "expressions", "columnName": "expressions", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "word_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`normalizedWord` TEXT NOT NULL, `encounterCount` INTEGER NOT NULL, `lastEncountered` INTEGER NOT NULL, PRIMARY KEY(`normalizedWord`))", "fields": [{"fieldPath": "normalizedWord", "columnName": "normalizedWord", "affinity": "TEXT", "notNull": true}, {"fieldPath": "encounterCount", "columnName": "encounterCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastEncountered", "columnName": "lastEncountered", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["normalizedWord"]}, "indices": [], "foreignKeys": []}, {"tableName": "expression_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`normalizedExpression` TEXT NOT NULL, `encounterCount` INTEGER NOT NULL, `lastEncountered` INTEGER NOT NULL, PRIMARY KEY(`normalizedExpression`))", "fields": [{"fieldPath": "normalizedExpression", "columnName": "normalizedExpression", "affinity": "TEXT", "notNull": true}, {"fieldPath": "encounterCount", "columnName": "encounterCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastEncountered", "columnName": "lastEncountered", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["normalizedExpression"]}, "indices": [], "foreignKeys": []}, {"tableName": "practice_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sessionId` TEXT NOT NULL, `userId` TEXT NOT NULL, `targetLanguage` TEXT NOT NULL, `primaryTranslationId` TEXT NOT NULL, `supplementaryTranslationIds` TEXT NOT NULL, `sessionType` TEXT NOT NULL, `totalItems` INTEGER NOT NULL, `completedItems` INTEGER NOT NULL, `averageScore` REAL NOT NULL, `sessionDuration` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `completedAt` INTEGER, `isCompleted` INTEGER NOT NULL, PRIMARY KEY(`sessionId`))", "fields": [{"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "primaryTranslationId", "columnName": "primaryTranslationId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "supplementaryTranslationIds", "columnName": "supplementaryTranslationIds", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionType", "columnName": "sessionType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalItems", "columnName": "totalItems", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completedItems", "columnName": "completedItems", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "sessionDuration", "columnName": "sessionDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completedAt", "columnName": "completedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["sessionId"]}, "indices": [{"name": "index_practice_sessions_userId_targetLanguage", "unique": false, "columnNames": ["userId", "targetLanguage"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_sessions_userId_targetLanguage` ON `${TABLE_NAME}` (`userId`, `targetLanguage`)"}, {"name": "index_practice_sessions_createdAt", "unique": false, "columnNames": ["createdAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_sessions_createdAt` ON `${TABLE_NAME}` (`createdAt`)"}], "foreignKeys": []}, {"tableName": "practice_items", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`itemId` TEXT NOT NULL, `content` TEXT NOT NULL, `pronunciation` TEXT, `translations` TEXT NOT NULL, `sourceTranslationId` TEXT NOT NULL, `itemType` TEXT NOT NULL, `difficultyLevel` TEXT NOT NULL, `masteryScore` REAL NOT NULL, `lastPracticed` INTEGER, `practiceCount` INTEGER NOT NULL, `isMarkedAsLearned` INTEGER NOT NULL, `frequencyScore` REAL NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`itemId`))", "fields": [{"fieldPath": "itemId", "columnName": "itemId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pronunciation", "columnName": "pronunciation", "affinity": "TEXT", "notNull": false}, {"fieldPath": "translations", "columnName": "translations", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceTranslationId", "columnName": "sourceTranslationId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemType", "columnName": "itemType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "difficultyLevel", "columnName": "difficultyLevel", "affinity": "TEXT", "notNull": true}, {"fieldPath": "masteryScore", "columnName": "masteryScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastPracticed", "columnName": "lastPracticed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "practiceCount", "columnName": "practiceCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isMarkedAsLearned", "columnName": "isMarkedAsLearned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "frequencyScore", "columnName": "frequencyScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["itemId"]}, "indices": [{"name": "index_practice_items_sourceTranslationId", "unique": false, "columnNames": ["sourceTranslationId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_items_sourceTranslationId` ON `${TABLE_NAME}` (`sourceTranslationId`)"}, {"name": "index_practice_items_itemType_difficultyLevel", "unique": false, "columnNames": ["itemType", "difficultyLevel"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_items_itemType_difficultyLevel` ON `${TABLE_NAME}` (`itemType`, `difficultyLevel`)"}, {"name": "index_practice_items_masteryScore", "unique": false, "columnNames": ["masteryScore"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_items_masteryScore` ON `${TABLE_NAME}` (`masteryScore`)"}, {"name": "index_practice_items_lastPracticed", "unique": false, "columnNames": ["lastPracticed"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_items_lastPracticed` ON `${TABLE_NAME}` (`lastPracticed`)"}], "foreignKeys": []}, {"tableName": "practice_results", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`resultId` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `itemId` TEXT NOT NULL, `itemType` TEXT NOT NULL, `listeningScore` REAL, `pronunciationScore` REAL, `comprehensionScore` REAL, `attempts` INTEGER NOT NULL, `timeSpent` INTEGER NOT NULL, `markedAsLearned` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`resultId`), FOREIGN KEY(`sessionId`) REFERENCES `practice_sessions`(`sessionId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "resultId", "columnName": "resultId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemId", "columnName": "itemId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemType", "columnName": "itemType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "listeningScore", "columnName": "listeningScore", "affinity": "REAL", "notNull": false}, {"fieldPath": "pronunciationScore", "columnName": "pronunciationScore", "affinity": "REAL", "notNull": false}, {"fieldPath": "comprehensionScore", "columnName": "comprehensionScore", "affinity": "REAL", "notNull": false}, {"fieldPath": "attempts", "columnName": "attempts", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeSpent", "columnName": "timeSpent", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "markedAsLearned", "columnName": "markedAsLearned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["resultId"]}, "indices": [{"name": "index_practice_results_sessionId", "unique": false, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_results_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}, {"name": "index_practice_results_itemId", "unique": false, "columnNames": ["itemId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_results_itemId` ON `${TABLE_NAME}` (`itemId`)"}, {"name": "index_practice_results_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_results_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}], "foreignKeys": [{"table": "practice_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sessionId"], "referencedColumns": ["sessionId"]}]}, {"tableName": "user_practice_profiles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`profileId` TEXT NOT NULL, `userId` TEXT NOT NULL, `targetLanguage` TEXT NOT NULL, `totalSessions` INTEGER NOT NULL, `totalPracticeTime` INTEGER NOT NULL, `averageSessionScore` REAL NOT NULL, `vocabularyMastery` REAL NOT NULL, `pronunciationMastery` REAL NOT NULL, `listeningMastery` REAL NOT NULL, `currentStreak` INTEGER NOT NULL, `longestStreak` INTEGER NOT NULL, `lastPracticeDate` INTEGER, `weakAreas` TEXT NOT NULL, `strongAreas` TEXT NOT NULL, `preferredDifficulty` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`profileId`))", "fields": [{"fieldPath": "profileId", "columnName": "profileId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalSessions", "columnName": "totalSessions", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPracticeTime", "columnName": "totalPracticeTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageSessionScore", "columnName": "averageSessionScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "vocabularyMastery", "columnName": "vocabularyMastery", "affinity": "REAL", "notNull": true}, {"fieldPath": "pronunciationMastery", "columnName": "pronunciationMastery", "affinity": "REAL", "notNull": true}, {"fieldPath": "listeningMastery", "columnName": "listeningMastery", "affinity": "REAL", "notNull": true}, {"fieldPath": "currentStreak", "columnName": "currentStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longestStreak", "columnName": "longestStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastPracticeDate", "columnName": "lastPracticeDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "preferred<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "preferred<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["profileId"]}, "indices": [{"name": "index_user_practice_profiles_userId_targetLanguage", "unique": true, "columnNames": ["userId", "targetLanguage"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_user_practice_profiles_userId_targetLanguage` ON `${TABLE_NAME}` (`userId`, `targetLanguage`)"}], "foreignKeys": []}, {"tableName": "session_analytics", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`analyticsId` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `userId` TEXT NOT NULL, `completionRate` REAL NOT NULL, `averageScore` REAL NOT NULL, `totalTimeSpent` INTEGER NOT NULL, `itemsCompleted` INTEGER NOT NULL, `itemsSkipped` INTEGER NOT NULL, `itemsMarkedAsLearned` INTEGER NOT NULL, `improvementAreas` TEXT NOT NULL, `achievements` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`analyticsId`), FOREIGN KEY(`sessionId`) REFERENCES `practice_sessions`(`sessionId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "analyticsId", "columnName": "analyticsId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "completionRate", "columnName": "completionRate", "affinity": "REAL", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalTimeSpent", "columnName": "totalTimeSpent", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "itemsCompleted", "columnName": "itemsCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "itemsSkipped", "columnName": "itemsSkipped", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "itemsMarkedAsLearned", "columnName": "itemsMarkedAsLearned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "improvementAreas", "columnName": "improvementAreas", "affinity": "TEXT", "notNull": true}, {"fieldPath": "achievements", "columnName": "achievements", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["analyticsId"]}, "indices": [{"name": "index_session_analytics_sessionId", "unique": true, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_session_analytics_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}], "foreignKeys": [{"table": "practice_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sessionId"], "referencedColumns": ["sessionId"]}]}, {"tableName": "item_mastery", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`masteryId` TEXT NOT NULL, `userId` TEXT NOT NULL, `itemId` TEXT NOT NULL, `itemContent` TEXT NOT NULL, `itemType` TEXT NOT NULL, `masteryLevel` REAL NOT NULL, `practiceCount` INTEGER NOT NULL, `successfulAttempts` INTEGER NOT NULL, `lastScore` REAL NOT NULL, `lastPracticed` INTEGER, `isMarkedAsLearned` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`masteryId`))", "fields": [{"fieldPath": "masteryId", "columnName": "masteryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemId", "columnName": "itemId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemContent", "columnName": "itemContent", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemType", "columnName": "itemType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "masteryLevel", "columnName": "masteryLevel", "affinity": "REAL", "notNull": true}, {"fieldPath": "practiceCount", "columnName": "practiceCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "successfulAttempts", "columnName": "successfulAttempts", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastScore", "columnName": "lastScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastPracticed", "columnName": "lastPracticed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isMarkedAsLearned", "columnName": "isMarkedAsLearned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["masteryId"]}, "indices": [{"name": "index_item_mastery_userId_itemId", "unique": true, "columnNames": ["userId", "itemId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_item_mastery_userId_itemId` ON `${TABLE_NAME}` (`userId`, `itemId`)"}, {"name": "index_item_mastery_masteryLevel", "unique": false, "columnNames": ["masteryLevel"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_item_mastery_masteryLevel` ON `${TABLE_NAME}` (`masteryLevel`)"}, {"name": "index_item_mastery_lastPracticed", "unique": false, "columnNames": ["lastPracticed"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_item_mastery_lastPracticed` ON `${TABLE_NAME}` (`lastPracticed`)"}], "foreignKeys": []}, {"tableName": "practice_streaks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`streakId` TEXT NOT NULL, `userId` TEXT NOT NULL, `targetLanguage` TEXT NOT NULL, `practiceDate` INTEGER NOT NULL, `sessionsCompleted` INTEGER NOT NULL, `totalPracticeTime` INTEGER NOT NULL, `averageScore` REAL NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`streakId`))", "fields": [{"fieldPath": "streakId", "columnName": "streakId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "practiceDate", "columnName": "practiceDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionsCompleted", "columnName": "sessionsCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPracticeTime", "columnName": "totalPracticeTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["streakId"]}, "indices": [{"name": "index_practice_streaks_userId_targetLanguage", "unique": false, "columnNames": ["userId", "targetLanguage"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_streaks_userId_targetLanguage` ON `${TABLE_NAME}` (`userId`, `targetLanguage`)"}, {"name": "index_practice_streaks_practiceDate", "unique": false, "columnNames": ["practiceDate"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_streaks_practiceDate` ON `${TABLE_NAME}` (`practiceDate`)"}], "foreignKeys": []}, {"tableName": "practice_achievements", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`achievementId` TEXT NOT NULL, `userId` TEXT NOT NULL, `achievementType` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `iconResource` TEXT NOT NULL, `targetLanguage` TEXT, `progress` REAL NOT NULL, `isUnlocked` INTEGER NOT NULL, `unlockedAt` INTEGER NOT NULL, PRIMARY KEY(`achievementId`))", "fields": [{"fieldPath": "achievementId", "columnName": "achievementId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "achievementType", "columnName": "achievementType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "iconResource", "columnName": "iconResource", "affinity": "TEXT", "notNull": true}, {"fieldPath": "targetLanguage", "columnName": "targetLanguage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "progress", "columnName": "progress", "affinity": "REAL", "notNull": true}, {"fieldPath": "isUnlocked", "columnName": "isUnlocked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unlockedAt", "columnName": "unlockedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["achievementId"]}, "indices": [{"name": "index_practice_achievements_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_achievements_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_practice_achievements_achievementType", "unique": false, "columnNames": ["achievementType"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_achievements_achievementType` ON `${TABLE_NAME}` (`achievementType`)"}, {"name": "index_practice_achievements_unlockedAt", "unique": false, "columnNames": ["unlockedAt"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_practice_achievements_unlockedAt` ON `${TABLE_NAME}` (`unlockedAt`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'abf38dc1a1dfddf4b9a5bcb9b661265d')"]}}