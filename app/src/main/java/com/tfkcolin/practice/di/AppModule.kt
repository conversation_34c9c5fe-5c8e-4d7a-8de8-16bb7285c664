package com.tfkcolin.practice.di

import android.app.Application
import android.content.Context
import androidx.room.Room
import com.google.firebase.auth.FirebaseAuth
import com.tfkcolin.practice.data.engine.AndroidSpeechEngine
import com.tfkcolin.practice.data.engine.AndroidTtsEngine
import com.tfkcolin.practice.data.engine.GoogleCloudTtsEngine
import com.tfkcolin.practice.data.model.AppDatabase
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.tts.data.TtsRepository
import com.tfkcolin.practice.features.auth.data.AuthRepository
import com.tfkcolin.practice.features.speech.data.SpeechRepository
import com.tfkcolin.practice.features.language.domain.LanguageManager
import com.tfkcolin.practice.features.audio.data.*
import com.tfkcolin.practice.features.text.data.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.scopes.ViewModelScoped
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TtsModule {
    @Provides
    @Singleton
    fun provideTtsRepository(
        @ApplicationContext context: Context,
    ): TtsRepository {
        val engines = mapOf(
            "android" to AndroidTtsEngine(context as Application),
            "google" to GoogleCloudTtsEngine()
        )
        return TtsRepository(engines)
    }
}

@Module
@InstallIn(SingletonComponent::class)
object AuthModule {
    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth = FirebaseAuth.getInstance()

    @Provides
    @Singleton
    fun provideAuthRepository(
        firebaseAuth: FirebaseAuth
    ): AuthRepository = AuthRepository(firebaseAuth)
}

@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {
    @Provides
    @Singleton
    fun provideSpeechRepository(
        @ApplicationContext context: Context,
    ): SpeechRepository {
        val engines = mapOf(
            "android" to AndroidSpeechEngine(context as Application)
        )
        return SpeechRepository(engines)
    }
}

@Module
@InstallIn(SingletonComponent::class)
object LanguageModule {
    @Provides
    @Singleton
    fun provideLanguageManager(
        ttsRepository: TtsRepository,
        speechRepository: SpeechRepository,
        preferencesRepository: PreferencesRepository
    ): LanguageManager = LanguageManager(ttsRepository, speechRepository, preferencesRepository)
}

@Module
@InstallIn(SingletonComponent::class)
object PreferencesModule {
    @Provides
    @Singleton
    fun providePreferencesRepository(
        @ApplicationContext context: Context
    ): PreferencesRepository = PreferencesRepository(context)
}

@Module
@InstallIn(SingletonComponent::class)
object AudioModule {
    @Provides
    @Singleton
    fun provideAudioTranscriptionService(): AudioTranscriptionService = FirebaseAudioTranscriptionService()

    @Provides
    @Singleton
    fun provideAudioMetadataService(): AudioMetadataService = AndroidAudioMetadataService()

    @Provides
    @Singleton
    fun provideAudioFileSplitterService(
        metadataService: AudioMetadataService
    ): AudioFileSplitterService = AndroidAudioFileSplitterService(metadataService)

    @Provides
    @Singleton
    fun provideAudioTranscriptionProcessor(
        metadataService: AudioMetadataService,
        splitterService: AudioFileSplitterService,
        transcriptionService: AudioTranscriptionService,
        textProcessingService: DocumentTextProcessingService
    ): AudioTranscriptionProcessor = EfficientAudioTranscriptionProcessor(
        metadataService,
        splitterService,
        transcriptionService,
        textProcessingService
    )
}

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            AppDatabase.DATABASE_NAME
        ).build()
    }
}

@Module
@InstallIn(SingletonComponent::class)
object TextProcessingModule {
    @Provides
    @Singleton
    fun provideImageTextExtractionService(): ImageTextExtractionService = MLKitImageTextExtractionService()

    @Provides
    @Singleton
    fun provideLanguageDetectionService(): LanguageDetectionService = MLKitLanguageDetectionService()

    @Provides
    @Singleton
    fun provideDocumentTextProcessingService(): DocumentTextProcessingService = BasicDocumentTextProcessingService()

    @Provides
    @Singleton
    fun provideAITextProcessingService(): AITextProcessingService = FirebaseAITextProcessingService()

    @Provides
    @Singleton
    fun provideTextProcessingPipeline(
        languageDetectionService: LanguageDetectionService,
        aiTextProcessingService: AITextProcessingService,
        translationRepository: TranslationRepository
    ): TextProcessingPipeline = UnifiedTextProcessingPipeline(
        languageDetectionService,
        aiTextProcessingService,
        translationRepository
    )
}
