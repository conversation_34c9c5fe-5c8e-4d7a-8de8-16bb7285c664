package com.tfkcolin.practice.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import androidx.navigation.NavType
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.features.auth.ui.AuthScreen
import com.tfkcolin.practice.features.speech.domain.SpeechViewModel
import com.tfkcolin.practice.features.home.domain.HomeViewModel
import com.tfkcolin.practice.features.language.ui.LanguageSelectionScreen
import com.tfkcolin.practice.features.settings.ui.SettingsScreen
import com.tfkcolin.practice.features.practice.ui.ListeningSpeakingPracticeScreen
import com.tfkcolin.practice.features.conversation.ui.ConversationPracticeScreen
import com.tfkcolin.practice.features.home.ui.HomeScreen
import com.tfkcolin.practice.features.audio.ui.AudioFileSelectionScreen
import com.tfkcolin.practice.features.audio.ui.AudioPracticeScreen
import com.tfkcolin.practice.features.content.ui.ContentInputSelectionScreen
import com.tfkcolin.practice.features.text.ui.DocumentImportScreen
import com.tfkcolin.practice.features.text.ui.ExtractedTextValidationScreen
import com.tfkcolin.practice.features.text.ui.LiveTextCaptureScreen
import com.tfkcolin.practice.features.text.ui.LiveTextRecognitionScreen

@Composable
fun AppNavGraph(
    modifier: Modifier = Modifier,
    startDestination: String,
    authViewModel: AuthViewModel,
    inAppUpdateManager: InAppUpdateManager,
    onRequestRecordAudioPermission: () -> Unit = {},
    onRequestAudioFileImport: () -> Unit = {},
    onSpeechViewModelReady: (SpeechViewModel) -> Unit = {},
    onHomeViewModelReady: (HomeViewModel) -> Unit = {},
    navController: NavHostController = rememberNavController()
) {
    NavHost(navController = navController, startDestination = startDestination) {
        // Authentication screen
        composable(Route.Auth.path) {
            val authState = authViewModel.authState.collectAsStateWithLifecycle()
            
            // Handle navigation when user successfully logs in
            LaunchedEffect(authState.value.isAuthenticated) {
                if (authState.value.isAuthenticated) {
                    navController.navigate(Route.LanguageSelection.path) {
                        popUpTo(Route.Auth.path) { inclusive = true }
                    }
                }
            }
            
            AuthScreen(authViewModel = authViewModel)
        }

        // Language Selection screen
        composable(Route.LanguageSelection.path) {
            LanguageSelectionScreen(
                onLanguageSelected = { language ->
                    // Navigate to home screen
                    navController.navigate(Route.Home.path) {
                        popUpTo(Route.LanguageSelection.path) { inclusive = true }
                    }
                },
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        // Settings screen
        composable(Route.Settings.path) {
            SettingsScreen(
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        // Home screen
        composable(Route.Home.path) {
            val homeViewModel: HomeViewModel = hiltViewModel()
            val authState = authViewModel.authState.collectAsStateWithLifecycle()

            // Register HomeViewModel for permission handling
            LaunchedEffect(homeViewModel) {
                onHomeViewModelReady(homeViewModel)
            }

            // Handle logout navigation
            LaunchedEffect(authState.value.isAuthenticated) {
                if (!authState.value.isAuthenticated) {
                    navController.navigate(Route.Auth.path) {
                        popUpTo(0) { inclusive = true } // Clear entire backstack
                    }
                }
            }

            HomeScreen(
                authViewModel = authViewModel,
                inAppUpdateManager = inAppUpdateManager,
                onNavigateToListeningPractice = { navController.navigate(Route.ListeningSpeakingPractice.path) },
                onNavigateToConversationPractice = { navController.navigate(Route.ConversationPractice.path) },
                onNavigateToContentInput = { navController.navigate(Route.ContentInputSelection.path) },
                onNavigateToSettings = { navController.navigate(Route.Settings.path) },
                onRequestRecordAudioPermission = onRequestRecordAudioPermission,
                onRequestAudioFileImport = onRequestAudioFileImport,
                homeViewModel = homeViewModel
            )
        }

        // Listening/Speaking Practice screen
        composable(Route.ListeningSpeakingPractice.path) {
            ListeningSpeakingPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }

        // Conversation Practice screen
        composable(Route.ConversationPractice.path) {
            ConversationPracticeScreen(
                onClose = { navController.popBackStack() }
            )
        }

        // Content Input Selection screen (unified)
        composable(Route.ContentInputSelection.path) {
            ContentInputSelectionScreen(
                onNavigateToAudioSelection = { navController.navigate(Route.AudioFileSelection.path) },
                onNavigateToImageCapture = { navController.navigate(Route.ImageCapture.path) },
                onNavigateToLiveTextRecognition = { navController.navigate(Route.LiveTextRecognition.path) },
                onNavigateToDocumentImport = { navController.navigate(Route.DocumentImport.path) },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Audio File Selection screen
        composable(Route.AudioFileSelection.path) {
            val homeViewModel: HomeViewModel = hiltViewModel()

            AudioFileSelectionScreen(
                onFileSelected = { audioFile ->
                    // Pass the selected file to AudioPracticeScreen via navigation argument
                    val encodedPath = audioFile.absolutePath.replace("/", "|")
                    navController.navigate(Route.AudioPractice.createRoute(encodedPath))
                },
                onBackPressed = { navController.popBackStack() },
                onRequestAudioFileImport = onRequestAudioFileImport,
                homeViewModel = homeViewModel
            )
        }
        
        // Audio Practice screen
        composable(
            route = Route.AudioPractice.path,
            arguments = listOf(
                navArgument("filePath") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val encodedFilePath = backStackEntry.arguments?.getString("filePath") ?: ""
            val filePath = encodedFilePath.replace("|", "/")

            val audioPracticeViewModel: com.tfkcolin.practice.features.audio.domain.AudioPracticeViewModel = hiltViewModel()

            AudioPracticeScreen(
                selectedFilePath = filePath,
                onClose = { navController.popBackStack() },
                onNavigateToTranslationResults = { translationId ->
                    navController.navigate(Route.TranslationResults.createRoute(translationId)) {
                        popUpTo(Route.AudioFileSelection.path)
                    }
                },
                onNavigateToValidation = { transcription ->
                    val filePath = audioPracticeViewModel.uiState.value.selectedFile?.absolutePath ?: ""
                    navController.navigate(Route.ExtractedTextValidation.createRouteWithData("audio", transcription, null, filePath))
                },
                viewModel = audioPracticeViewModel
            )
        }


        // Image Capture screen
        composable(Route.ImageCapture.path) {
            val imageCaptureViewModel: com.tfkcolin.practice.features.text.domain.ImageCaptureViewModel = hiltViewModel()

            com.tfkcolin.practice.features.text.ui.ImageCaptureScreen(
                onImageProcessed = { extractedText, detectedLanguage, sourceUri ->
                    // Navigate to text validation with extracted text
                    navController.navigate(
                        Route.ExtractedTextValidation.createRouteWithData("image", extractedText, detectedLanguage, sourceUri)
                    )
                },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Live Text Recognition screen
        composable(Route.LiveTextRecognition.path) {
            LiveTextRecognitionScreen(
                onNavigateToCapture = { extractedText ->
                    // Navigate to live text capture screen
                    navController.navigate(Route.LiveTextCapture.createRoute(extractedText))
                },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Live Text Capture screen
        composable(
            route = Route.LiveTextCapture.path,
            arguments = listOf(
                navArgument("text") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val encodedText = backStackEntry.arguments?.getString("text") ?: ""
            val extractedText = encodedText.replace("|", "/")

            LiveTextCaptureScreen(
                extractedText = extractedText,
                onAccept = { validatedText ->
                    // Navigate to text processing with validated text
                    navController.navigate(
                        Route.TextProcessing.createRouteWithData("live_text", validatedText, null)
                    )
                },
                onReject = { navController.popBackStack() },
                onBackPressed = { navController.popBackStack() },
                sourceInfo = "Live Capture"
            )
        }

        // Document Import screen
        composable(Route.DocumentImport.path) {
            DocumentImportScreen(
                onDocumentExtracted = { extractedText, sourceName ->
                    // Navigate to text validation with extracted text
                    navController.navigate(
                        Route.ExtractedTextValidation.createRouteWithData("document", extractedText, null, sourceName)
                    )
                },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Extracted Text Validation screen
        composable(
            route = Route.ExtractedTextValidation.path,
            arguments = listOf(
                navArgument("inputType") { type = NavType.StringType },
                navArgument("text") { type = NavType.StringType },
                navArgument("lang") { type = NavType.StringType },
                navArgument("source") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val inputType = backStackEntry.arguments?.getString("inputType") ?: "unknown"
            val encodedText = backStackEntry.arguments?.getString("text") ?: ""
            val detectedLanguage = backStackEntry.arguments?.getString("lang")
            val encodedSource = backStackEntry.arguments?.getString("source") ?: "none"
            val extractedText = encodedText.replace("|", "/").replace(";", ":")
            val sourceInfo = encodedSource.takeIf { it != "none" }?.replace("|", "/")?.replace(";", ":")

            ExtractedTextValidationScreen(
                inputType = inputType,
                extractedText = extractedText,
                detectedLanguage = detectedLanguage?.takeIf { it != "auto" },
                sourceInfo = sourceInfo,
                onProceed = { validatedText ->
                    navController.navigate(
                        Route.TextProcessing.createRouteWithData(inputType, validatedText, detectedLanguage)
                    )
                },
                onReject = { navController.popBackStack() },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Text Processing screen
        composable(
            route = Route.TextProcessing.path,
            arguments = listOf(
                navArgument("inputType") { type = NavType.StringType },
                navArgument("text") { type = NavType.StringType },
                navArgument("lang") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val inputType = backStackEntry.arguments?.getString("inputType") ?: "unknown"
            val encodedText = backStackEntry.arguments?.getString("text") ?: ""
            val detectedLanguage = backStackEntry.arguments?.getString("lang")
            val currentRoute = backStackEntry.destination.route ?: ""

            // Decode the text (simple replacement for the | character)
            val extractedText = encodedText.replace("|", "/")

            val textProcessingViewModel: com.tfkcolin.practice.features.text.domain.TextProcessingViewModel = hiltViewModel()

            com.tfkcolin.practice.features.text.ui.TextProcessingScreen(
                extractedText = extractedText,
                detectedLanguage = detectedLanguage?.takeIf { it != "auto" },
                inputType = inputType,
                onProcessingComplete = { translationId ->
                    navController.navigate(Route.TranslationResults.createRoute(translationId)) {
                        popUpTo(currentRoute) { inclusive = true }
                    }
                },
                onBackPressed = { navController.popBackStack() }
            )
        }

        // Translation Results screen
        composable(
            route = Route.TranslationResults.path,
            arguments = listOf(
                navArgument("translationId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val translationId = backStackEntry.arguments?.getString("translationId") ?: ""

            com.tfkcolin.practice.features.text.ui.TranslationResultsScreen(
                onBackPressed = { navController.popBackStack() }
            )
        }
    }
}
