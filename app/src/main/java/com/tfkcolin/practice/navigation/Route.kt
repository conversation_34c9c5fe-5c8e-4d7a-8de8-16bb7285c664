package com.tfkcolin.practice.navigation

/**
 * Sealed class representing all navigation routes in the app.
 * This provides type safety and prevents string duplication.
 */
sealed class Route(val path: String) {
    data object Auth : Route("auth")
    data object LanguageSelection : Route("language_selection")
    data object Home : Route("home")
    data object Settings : Route("settings")
    data object TtsMain : Route("tts_main")
    data object SpeechMain : Route("speech_main")
    data object ListeningSpeakingPractice : Route("listening_speaking_practice")
    data object ConversationPractice : Route("conversation_practice")
    // Content input routes
    data object ContentInputSelection : Route("content_input_selection")
    data object AudioFileSelection : Route("audio_file_selection")
    data object AudioPractice : Route("audio_practice/{filePath}") {
        fun createRoute(filePath: String): String = "audio_practice/${filePath.replace("/", "|")}"
    }
    data object ImageCapture : Route("image_capture")
    data object LiveTextRecognition : Route("live_text_recognition")
    data object LiveTextCapture : Route("live_text_capture/{text}") {
        fun createRoute(text: String): String = "live_text_capture/${text.take(50).replace("/", "|")}"
    }
    data object DocumentImport : Route("document_import")
    data object ExtractedTextValidation : Route("extracted_text_validation/{inputType}/{text}/{lang}/{source}") {
            fun createRouteWithData(inputType: String, text: String, lang: String?, source: String? = null): String {
                val safeText = text.take(100).replace("/", "|").replace(":", ";") // Handle URIs properly
                val safeLang = lang ?: "auto"
                val safeSource = source?.take(200)?.replace("/", "|")?.replace(":", ";") ?: "none" // Allow longer for URIs
                return "extracted_text_validation/$inputType/$safeText/$safeLang/$safeSource"
            }
        }
    data object TextProcessing : Route("text_processing/{inputType}/{text}/{lang}") {
        fun createRoute(inputType: String): String = "text_processing/$inputType/placeholder/placeholder"
        fun createRouteWithData(inputType: String, text: String, lang: String?): String {
            val safeText = text.take(50).replace("/", "|") // Simple encoding to avoid path issues
            val safeLang = lang ?: "auto"
            return "text_processing/$inputType/$safeText/$safeLang"
        }
    }
    data object TranslationResults : Route("translation_results/{translationId}") {
        fun createRoute(translationId: String): String = "translation_results/$translationId"
    }
}
