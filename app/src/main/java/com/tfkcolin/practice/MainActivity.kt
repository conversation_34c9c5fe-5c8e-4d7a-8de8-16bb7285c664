package com.tfkcolin.practice

import android.Manifest
import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.core.InAppUpdateManager
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.navigation.AppNavGraph
import com.tfkcolin.practice.navigation.Route
import com.tfkcolin.practice.ui.components.LoadingScreen
import com.tfkcolin.practice.ui.theme.PracticeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val requestRecordAudioPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        // Handle permission result - this will be passed to ViewModel later
        onRecordAudioPermissionResult(isGranted)
    }

    private val audioFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            homeViewModel?.importAudioFile(it)
        }
    }

    // Note: Google Play Core library's in-app update flow still requires onActivityResult
    // The library doesn't yet support Activity Result API directly

    private var speechViewModel: com.tfkcolin.practice.features.speech.domain.SpeechViewModel? = null
    private var homeViewModel: com.tfkcolin.practice.features.home.domain.HomeViewModel? = null
    private lateinit var inAppUpdateManager: InAppUpdateManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Initialize in-app update manager
        inAppUpdateManager = InAppUpdateManager(this)
        inAppUpdateManager.checkForUpdate()

        // Handle navigation from ShareActivity
        val navigationRoute = intent.getStringExtra("navigation_route")
        if (navigationRoute != null && savedInstanceState == null) {
            // Store the navigation route to be handled after auth check
            intent.putExtra("pending_navigation_route", navigationRoute)
            // Clear the original extra to prevent re-processing
            intent.removeExtra("navigation_route")
        }

        setContent {
            PracticeTheme {
                val authViewModel: AuthViewModel = hiltViewModel()
                val authState by authViewModel.authState.collectAsStateWithLifecycle()

                // Show loading screen while auth state is being determined
                if (authState.isLoading) {
                    LoadingScreen(message = "Checking authentication...")
                } else {
                    var startDest = if (authState.isAuthenticated) Route.Home.path else Route.Auth.path

                    // Check for pending navigation from ShareActivity
                    val pendingRoute = intent.getStringExtra("pending_navigation_route")
                    if (pendingRoute != null && authState.isAuthenticated) {
                        startDest = pendingRoute
                        intent.removeExtra("pending_navigation_route")
                    }

                    Scaffold {
                        AppNavGraph(
                            modifier = Modifier.padding(it),
                            startDestination = startDest,
                            authViewModel = authViewModel,
                            inAppUpdateManager = inAppUpdateManager,
                            onRequestRecordAudioPermission = {
                                requestRecordAudioPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                            },
                            onRequestAudioFileImport = {
                                audioFilePickerLauncher.launch("audio/*")
                            },
                            onSpeechViewModelReady = { viewModel ->
                                speechViewModel = viewModel
                            },
                            onHomeViewModelReady = { viewModel ->
                                homeViewModel = viewModel
                            }
                        )
                    }
                }
            }
        }
    }



    // Required for Google Play Core in-app updates (library doesn't support Activity Result API yet)
    @Deprecated("Required for Google Play Core in-app updates")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: android.content.Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == InAppUpdateManager.REQUEST_CODE_UPDATE) {
            inAppUpdateManager.handleUpdateResult(resultCode)
        }
    }

    private fun onRecordAudioPermissionResult(granted: Boolean) {
        speechViewModel?.onPermissionResult(granted)
        homeViewModel?.onPermissionResult(granted)
    }
}

