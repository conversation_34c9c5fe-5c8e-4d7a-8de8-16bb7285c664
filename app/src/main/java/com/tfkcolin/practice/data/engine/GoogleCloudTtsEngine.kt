package com.tfkcolin.practice.data.engine

import java.util.Locale

class GoogleCloudTtsEngine: TtsEngine {
    override fun init(callback: (Boolean) -> Unit) {
        // Google Cloud TTS not yet implemented, return false
        callback(false)
    }

    override fun shutdown() {
        TODO("Not yet implemented")
    }

    override fun speak(text: String) {
        TODO("Not yet implemented")
    }

    override fun setLanguage(locale: Locale) {
        TODO("Not yet implemented")
    }

    override fun getAvailableLanguages(): List<Locale> {
        TODO("Not yet implemented")
    }

    override val displayName: String
        get() = "Google Cloud"
}