package com.tfkcolin.practice.data.model

import androidx.room.TypeConverter
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class PracticeConverters {

    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromPracticeItemType(value: PracticeItemType): String {
        return value.name
    }

    @TypeConverter
    fun toPracticeItemType(value: String): PracticeItemType {
        return PracticeItemType.valueOf(value)
    }

    @TypeConverter
    fun fromSessionType(value: SessionType): String {
        return value.name
    }

    @TypeConverter
    fun toSessionType(value: String): SessionType {
        return SessionType.valueOf(value)
    }

    @TypeConverter
    fun fromDifficultyLevel(value: DifficultyLevel): String {
        return value.name
    }

    @TypeConverter
    fun toDifficultyLevel(value: String): DifficultyLevel {
        return DifficultyLevel.valueOf(value)
    }

    @TypeConverter
    fun fromAchievementType(value: AchievementType): String {
        return value.name
    }

    @TypeConverter
    fun toAchievementType(value: String): AchievementType {
        return AchievementType.valueOf(value)
    }

    @TypeConverter
    fun fromPracticeMode(value: PracticeMode): String {
        return value.name
    }

    @TypeConverter
    fun toPracticeMode(value: String): PracticeMode {
        return PracticeMode.valueOf(value)
    }

    @TypeConverter
    fun fromPronunciationAnalysis(value: PronunciationAnalysis): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPronunciationAnalysis(value: String): PronunciationAnalysis {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromPhonemeComparisonList(value: List<PhonemeComparison>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPhonemeComparisonList(value: String): List<PhonemeComparison> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromPronunciationSuggestionList(value: List<PronunciationSuggestion>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPronunciationSuggestionList(value: String): List<PronunciationSuggestion> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromSuggestionType(value: SuggestionType): String {
        return value.name
    }

    @TypeConverter
    fun toSuggestionType(value: String): SuggestionType {
        return SuggestionType.valueOf(value)
    }

    @TypeConverter
    fun fromPracticeItemList(value: List<PracticeItem>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPracticeItemList(value: String): List<PracticeItem> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromPracticeResultList(value: List<PracticeResult>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPracticeResultList(value: String): List<PracticeResult> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromTranslationList(value: List<Translation>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toTranslationList(value: String): List<Translation> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromTranslation(value: Translation): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toTranslation(value: String): Translation {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromPracticeSession(value: PracticeSession): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPracticeSession(value: String): PracticeSession {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromAssessmentResult(value: AssessmentResult): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toAssessmentResult(value: String): AssessmentResult {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromAssessmentMode(value: AssessmentMode): String {
        return value.name
    }

    @TypeConverter
    fun toAssessmentMode(value: String): AssessmentMode {
        return AssessmentMode.valueOf(value)
    }

    @TypeConverter
    fun fromPerformanceMetrics(value: PerformanceMetrics): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toPerformanceMetrics(value: String): PerformanceMetrics {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromUserPracticeProfile(value: UserPracticeProfile): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toUserPracticeProfile(value: String): UserPracticeProfile {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromLearningAnalytics(value: LearningAnalytics): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toLearningAnalytics(value: String): LearningAnalytics {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromItemAnalyticsList(value: List<ItemAnalytics>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toItemAnalyticsList(value: String): List<ItemAnalytics> {
        return Json.decodeFromString(value)
    }
}
