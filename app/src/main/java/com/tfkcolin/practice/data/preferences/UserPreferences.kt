package com.tfkcolin.practice.data.preferences

import com.tfkcolin.practice.features.language.domain.SupportedLanguage
import java.util.Locale

/**
 * Data class representing user preferences that should be persisted
 */
data class UserPreferences(
    val selectedLanguageCode: String? = null, // e.g., "en", "es", "fr"
    val selectedLanguageCountry: String? = null, // e.g., "US", "ES" for locale specificity
    val selectedTargetLanguage: String = "en", // Target language for translations
    val hasCompletedLanguageSelection: Boolean = false
) {
    /**
     * Converts the preferences back to a SupportedLanguage if possible
     */
    fun toSupportedLanguage(): SupportedLanguage? {
        return if (selectedLanguageCode != null) {
            val locale = if (selectedLanguageCountry != null) {
                Locale(selectedLanguageCode, selectedLanguageCountry)
            } else {
                Locale(selectedLanguageCode)
            }

            SupportedLanguage(
                locale = locale,
                displayName = locale.getDisplayName(locale),
                ttsEngineKey = "android", // Default, will be determined by LanguageManager
                speechEngineKey = "android",
                isAvailable = true
            )
        } else {
            null
        }
    }

    companion object {
        fun fromSupportedLanguage(language: SupportedLanguage?, hasCompletedSelection: Boolean = true): UserPreferences {
            return UserPreferences(
                selectedLanguageCode = language?.locale?.language,
                selectedLanguageCountry = language?.locale?.country?.takeIf { it.isNotEmpty() },
                selectedTargetLanguage = "en", // Default target language
                hasCompletedLanguageSelection = hasCompletedSelection
            )
        }
    }
}