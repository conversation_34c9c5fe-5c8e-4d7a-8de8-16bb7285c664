package com.tfkcolin.practice.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "word_stats")
data class WordStats(
    @PrimaryKey val normalizedWord: String,
    val encounterCount: Int = 1,
    val lastEncountered: Long = System.currentTimeMillis()
)

@Entity(tableName = "expression_stats")
data class ExpressionStats(
    @PrimaryKey val normalizedExpression: String,
    val encounterCount: Int = 1,
    val lastEncountered: Long = System.currentTimeMillis()
)