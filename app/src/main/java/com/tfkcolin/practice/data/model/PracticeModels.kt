package com.tfkcolin.practice.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import kotlinx.serialization.Serializable
import java.util.UUID

// Core practice session data
@Serializable
data class PracticeSession(
    val sessionId: String = UUID.randomUUID().toString(),
    val targetLanguage: String,
    val primaryTranslation: Translation,
    val supplementaryTranslations: List<Translation> = emptyList(),
    val practiceItems: List<PracticeItem>,
    val minimumItems: Int = 20,
    val sessionType: SessionType = SessionType.MIXED,
    val createdAt: Long = System.currentTimeMillis()
)

@Serializable
data class PracticeItem(
    val id: String = UUID.randomUUID().toString(),
    val content: String, // Word or expression text
    val pronunciation: String?,
    val translations: List<String>,
    val sourceTranslationId: String,
    val itemType: PracticeItemType,
    val difficultyLevel: DifficultyLevel,
    val masteryScore: Float = 0f, // 0.0 to 1.0
    val lastPracticed: Long? = null,
    val practiceCount: Int = 0,
    val isMarkedAsLearned: Boolean = false,
    val frequencyScore: Float = 0f // Based on encounter count
)

@Serializable
enum class PracticeItemType {
    WORD, EXPRESSION, PHRASE
}

@Serializable
enum class SessionType {
    SINGLE_TEXT,    // All items from one translation
    MIXED,          // Items from multiple translations
    REVIEW          // Previously learned items for reinforcement
}

@Serializable
enum class DifficultyLevel {
    BEGINNER, INTERMEDIATE, ADVANCED
}

@Serializable
enum class PracticeMode {
    LISTENING,      // Listen to pronunciation
    SPEAKING,       // Practice pronunciation
    COMPREHENSION,  // Understand meaning
    FINAL_READING,  // Read full text
    FINAL_LISTENING // Listen to full text
}

// Practice flow state
@Serializable
data class PracticeFlowState(
    val currentSession: PracticeSession,
    val currentItemIndex: Int = 0,
    val practiceMode: PracticeMode = PracticeMode.LISTENING,
    val userResponse: String = "",
    val pronunciationScore: Float? = null,
    val transcriptionResult: String? = null,
    val showFeedback: Boolean = false,
    val sessionProgress: Float = 0f,
    val completedItems: List<PracticeResult> = emptyList(),
    val isSessionComplete: Boolean = false,
    val finalAssessmentMode: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null
)

@Serializable
data class PracticeResult(
    val item: PracticeItem,
    val listeningScore: Float? = null,
    val pronunciationScore: Float? = null,
    val comprehensionScore: Float? = null,
    val attempts: Int = 0,
    val timeSpent: Long = 0,
    val markedAsLearned: Boolean = false,
    val completedAt: Long = System.currentTimeMillis()
)

// Pronunciation analysis
@Serializable
data class PronunciationAnalysis(
    val targetText: String,
    val userTranscription: String,
    val overallScore: Float, // 0.0 to 1.0
    val phonemeScores: List<PhonemeComparison> = emptyList(),
    val suggestions: List<PronunciationSuggestion> = emptyList(),
    val canPlayUserAudio: Boolean = false,
    val canPlayReferenceAudio: Boolean = true
)

@Serializable
data class PhonemeComparison(
    val phoneme: String,
    val accuracy: Float, // 0.0 to 1.0
    val feedback: String
)

@Serializable
data class PronunciationSuggestion(
    val type: SuggestionType,
    val message: String,
    val targetPhoneme: String? = null
)

@Serializable
enum class SuggestionType {
    PHONEME_ACCURACY,
    RHYTHM_TIMING,
    STRESS_PATTERN,
    GENERAL_TIP
}

// Assessment results
@Serializable
data class AssessmentResult(
    val sessionId: String,
    val readingScore: Float,
    val listeningScore: Float,
    val overallScore: Float,
    val completedAt: Long = System.currentTimeMillis()
)

@Serializable
enum class AssessmentMode {
    READING, LISTENING
}

// Difficulty adjustment
@Serializable
enum class DifficultyAdjustment {
    INCREASE, DECREASE, MAINTAIN
}

@Serializable
data class PerformanceMetrics(
    val averageScore: Float,
    val totalAttempts: Int,
    val successRate: Float,
    val averageTimePerItem: Long,
    val improvementTrend: Float // Positive = improving, negative = declining
)

// Content selection criteria
@Serializable
data class ContentSelectionCriteria(
    val targetLanguage: String,
    val sessionSize: Int = 20,
    val difficultyRange: Pair<DifficultyLevel, DifficultyLevel> = Pair(DifficultyLevel.BEGINNER, DifficultyLevel.ADVANCED),
    val includeReviewItems: Boolean = true,
    val maxReviewPercentage: Float = 0.3f, // Max 30% review items
    val prioritizeWeakAreas: Boolean = true
)

// User progress tracking
@Serializable
data class UserPracticeProfile(
    val userId: String,
    val targetLanguage: String,
    val totalSessions: Int = 0,
    val totalPracticeTime: Long = 0,
    val averageSessionScore: Float = 0f,
    val vocabularyMastery: Float = 0f,
    val pronunciationMastery: Float = 0f,
    val listeningMastery: Float = 0f,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val lastPracticeDate: Long? = null,
    val weakAreas: List<String> = emptyList(),
    val strongAreas: List<String> = emptyList(),
    val preferredDifficulty: DifficultyLevel = DifficultyLevel.BEGINNER
)

// Learning analytics
@Serializable
data class LearningAnalytics(
    val sessionId: String,
    val userId: String,
    val itemAnalytics: List<ItemAnalytics>,
    val sessionDuration: Long,
    val completionRate: Float,
    val averageScore: Float,
    val improvementAreas: List<String>,
    val achievements: List<String> = emptyList()
)

@Serializable
data class ItemAnalytics(
    val itemId: String,
    val itemType: PracticeItemType,
    val attempts: Int,
    val timeSpent: Long,
    val finalScore: Float,
    val difficultyLevel: DifficultyLevel,
    val wasMarkedAsLearned: Boolean
)
