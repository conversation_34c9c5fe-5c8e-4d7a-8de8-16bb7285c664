package com.tfkcolin.practice.data.model

import androidx.room.TypeConverter
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class TranslationConverters {

    @TypeConverter
    fun fromWordDetailList(value: List<WordDetail>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toWordDetailList(value: String): List<WordDetail> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromExpressionExampleList(value: List<ExpressionExample>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toExpressionExampleList(value: String): List<ExpressionExample> {
        return Json.decodeFromString(value)
    }

    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return Json.encodeToString(value)
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        return Json.decodeFromString(value)
    }
}