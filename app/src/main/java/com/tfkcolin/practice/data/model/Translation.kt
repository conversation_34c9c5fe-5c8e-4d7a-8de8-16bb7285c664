package com.tfkcolin.practice.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import kotlinx.serialization.Serializable

@Serializable
@Entity(tableName = "translations")
@TypeConverters(TranslationConverters::class)
data class Translation(
    @PrimaryKey val id: String,
    val originalText: String,
    val translatedText: String,
    val originalTextPronunciations: String?, // Can be null if not provided
    val sourceLanguage: String,
    val targetLanguage: String,
    val timestamp: String,
    val source: String, // "image" or "document"
    val isStarred: Boolean,
    val words: List<WordDetail>,
    val expressions: List<ExpressionExample>
)

// For the breakdown of a single word
@Serializable
data class WordDetail(
    val word: String,
    val normalizedWord: String, // lowercase for matching
    val pronunciation: String?,
    val translations: List<String>
)

// For example sentences or expressions
@Serializable
data class ExpressionExample(
    val sourceExpression: String,
    val translatedExpression: String,
    val normalizedExpression: String, // lowercase for matching
    val pronunciation: String?,
)