package com.tfkcolin.practice.data.engine

import android.app.Application
import android.speech.tts.TextToSpeech
import java.util.Locale

class AndroidTtsEngine(private val context: Application): TtsEngine {
    private var tts: TextToSpeech? = null

    private var currentLocale: Locale = Locale.getDefault()

    override fun init(callback: (Boolean) -> Unit) {
        tts = TextToSpeech(context) { status ->
            val ok = status == TextToSpeech.SUCCESS
            if(ok) tts?.language = currentLocale
            callback(ok)
        }
    }

    override fun speak(text: String) {
        tts?.speak(text, TextToSpeech.QUEUE_FLUSH, null, null)
    }

    override fun setLanguage(locale: Locale) {
        currentLocale = locale
        tts?.language = locale
    }

    override fun getAvailableLanguages(): List<Locale> {
        return tts?.availableLanguages?.toList() ?: emptyList()
    }

    override fun shutdown() {
        tts?.stop()
        tts?.shutdown()
    }

    override val displayName: String
        get() = "System (Android)"
}