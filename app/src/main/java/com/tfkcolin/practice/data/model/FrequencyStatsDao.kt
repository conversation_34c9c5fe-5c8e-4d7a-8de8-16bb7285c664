package com.tfkcolin.practice.data.model

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface FrequencyStatsDao {

    // Word Stats
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWordStat(wordStat: WordStats)

    @Update
    suspend fun updateWordStat(wordStat: WordStats)

    @Query("SELECT * FROM word_stats WHERE normalizedWord = :normalizedWord")
    suspend fun getWordStat(normalizedWord: String): WordStats?

    @Query("SELECT * FROM word_stats ORDER BY encounterCount DESC, lastEncountered DESC")
    fun getAllWordStats(): Flow<List<WordStats>>

    @Query("SELECT * FROM word_stats ORDER BY encounterCount DESC LIMIT :limit")
    suspend fun getTopWordStats(limit: Int): List<WordStats>

    // Expression Stats
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExpressionStat(expressionStat: ExpressionStats)

    @Update
    suspend fun updateExpressionStat(expressionStat: ExpressionStats)

    @Query("SELECT * FROM expression_stats WHERE normalizedExpression = :normalizedExpression")
    suspend fun getExpressionStat(normalizedExpression: String): ExpressionStats?

    @Query("SELECT * FROM expression_stats ORDER BY encounterCount DESC, lastEncountered DESC")
    fun getAllExpressionStats(): Flow<List<ExpressionStats>>

    @Query("SELECT * FROM expression_stats ORDER BY encounterCount DESC LIMIT :limit")
    suspend fun getTopExpressionStats(limit: Int): List<ExpressionStats>
}