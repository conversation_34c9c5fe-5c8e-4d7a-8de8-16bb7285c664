package com.tfkcolin.practice.data.engine

import android.app.Application
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import java.util.Locale

class AndroidSpeechEngine(private val context: Application) : SpeechEngine {
    private var speechRecognizer: SpeechRecognizer? = null
    private var currentLocale: Locale = Locale.getDefault()
    private var recognitionCallback: ((String) -> Unit)? = null
    private var errorCallback: ((String) -> Unit)? = null
    private var _isListening = false

    override val isListening: Boolean get() = _isListening

    override fun init(callback: (Boolean) -> Unit) {
        val isAvailable = SpeechRecognizer.isRecognitionAvailable(context)
        if (isAvailable) {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
            speechRecognizer?.setRecognitionListener(createRecognitionListener())
        }
        callback(isAvailable)
    }

    override fun startListening(callback: (String) -> Unit, errorCallback: (String) -> Unit) {
        this.recognitionCallback = callback
        this.errorCallback = errorCallback
        
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, currentLocale.toLanguageTag())
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }
        
        _isListening = true
        speechRecognizer?.startListening(intent)
    }

    override fun stopListening() {
        _isListening = false
        speechRecognizer?.stopListening()
    }

    override fun setLanguage(locale: Locale) {
        currentLocale = locale
    }

    override fun getAvailableLanguages(): List<Locale> {
        // Android Speech Recognition supports many languages
        return listOf(
            Locale.ENGLISH,
            Locale("es", "ES"),
            Locale.FRENCH,
            Locale.GERMAN,
            Locale.ITALIAN,
            Locale.JAPANESE,
            Locale.KOREAN,
            Locale.CHINESE,
            currentLocale
        ).distinct()
    }

    override fun shutdown() {
        stopListening()
        speechRecognizer?.destroy()
        speechRecognizer = null
    }

    override val displayName: String = "System (Android)"

    private fun createRecognitionListener() = object : RecognitionListener {
        override fun onReadyForSpeech(params: Bundle?) {}
        override fun onBeginningOfSpeech() {}
        override fun onRmsChanged(rmsdB: Float) {}
        override fun onBufferReceived(buffer: ByteArray?) {}
        override fun onEndOfSpeech() {
            _isListening = false
        }

        override fun onError(error: Int) {
            _isListening = false
            val errorMessage = when (error) {
                SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                SpeechRecognizer.ERROR_NETWORK -> "Network error"
                SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                SpeechRecognizer.ERROR_NO_MATCH -> "No speech input"
                SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                SpeechRecognizer.ERROR_SERVER -> "Server error"
                SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                else -> "Unknown error"
            }
            errorCallback?.invoke(errorMessage)
        }

        override fun onResults(results: Bundle?) {
            _isListening = false
            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
            matches?.firstOrNull()?.let { recognitionCallback?.invoke(it) }
        }

        override fun onPartialResults(partialResults: Bundle?) {
            val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
            matches?.firstOrNull()?.let { recognitionCallback?.invoke(it) }
        }

        override fun onEvent(eventType: Int, params: Bundle?) {}
    }
}