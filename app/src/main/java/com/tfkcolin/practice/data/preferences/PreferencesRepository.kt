package com.tfkcolin.practice.data.preferences

import android.content.Context
import android.content.SharedPreferences
import com.tfkcolin.practice.features.language.domain.SupportedLanguage
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing user preferences using SharedPreferences
 */
@Singleton
class PreferencesRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val _userPreferences = MutableStateFlow(loadUserPreferences())
    val userPreferences: Flow<UserPreferences> = _userPreferences.asStateFlow()

    companion object {
        private const val PREFS_NAME = "user_preferences"
        private const val KEY_SELECTED_LANGUAGE_CODE = "selected_language_code"
        private const val KEY_SELECTED_LANGUAGE_COUNTRY = "selected_language_country"
        private const val KEY_SELECTED_TARGET_LANGUAGE = "selected_target_language"
        private const val KEY_COMPLETED_LANGUAGE_SELECTION = "completed_language_selection"
    }

    /**
     * Saves the user's language selection
     */
    fun saveSelectedLanguage(language: SupportedLanguage) {
        val preferences = UserPreferences.fromSupportedLanguage(language, hasCompletedSelection = true)
        saveUserPreferences(preferences)
    }

    /**
     * Marks language selection as completed without changing the language
     */
    fun markLanguageSelectionCompleted() {
        val currentPrefs = _userPreferences.value
        val updatedPrefs = currentPrefs.copy(hasCompletedLanguageSelection = true)
        saveUserPreferences(updatedPrefs)
    }

    /**
     * Clears the selected language (resets to default)
     */
    fun clearSelectedLanguage() {
        val preferences = UserPreferences(
            selectedLanguageCode = null,
            selectedLanguageCountry = null,
            selectedTargetLanguage = "en", // Keep default target language
            hasCompletedLanguageSelection = false
        )
        saveUserPreferences(preferences)
    }

    /**
     * Gets the current user preferences synchronously
     */
    fun getCurrentPreferences(): UserPreferences {
        return _userPreferences.value
    }

    /**
     * Saves the selected target language for translations
     */
    fun saveTargetLanguage(targetLanguage: String) {
        val currentPrefs = _userPreferences.value
        val updatedPrefs = currentPrefs.copy(selectedTargetLanguage = targetLanguage)
        saveUserPreferences(updatedPrefs)
    }

    private fun saveUserPreferences(preferences: UserPreferences) {
        prefs.edit().apply {
            putString(KEY_SELECTED_LANGUAGE_CODE, preferences.selectedLanguageCode)
            putString(KEY_SELECTED_LANGUAGE_COUNTRY, preferences.selectedLanguageCountry)
            putString(KEY_SELECTED_TARGET_LANGUAGE, preferences.selectedTargetLanguage)
            putBoolean(KEY_COMPLETED_LANGUAGE_SELECTION, preferences.hasCompletedLanguageSelection)
        }.apply()

        _userPreferences.value = preferences
    }

    private fun loadUserPreferences(): UserPreferences {
        return UserPreferences(
            selectedLanguageCode = prefs.getString(KEY_SELECTED_LANGUAGE_CODE, null),
            selectedLanguageCountry = prefs.getString(KEY_SELECTED_LANGUAGE_COUNTRY, null),
            selectedTargetLanguage = prefs.getString(KEY_SELECTED_TARGET_LANGUAGE, "en") ?: "en",
            hasCompletedLanguageSelection = prefs.getBoolean(KEY_COMPLETED_LANGUAGE_SELECTION, false)
        )
    }

    /**
     * Clears all preferences (useful for testing or logout scenarios)
     */
    fun clearAllPreferences() {
        prefs.edit().clear().apply()
        _userPreferences.value = UserPreferences()
    }
}