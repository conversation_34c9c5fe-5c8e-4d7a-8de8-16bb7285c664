package com.tfkcolin.practice.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import androidx.room.ForeignKey
import androidx.room.Index

// Practice session entity
@Entity(
    tableName = "practice_sessions",
    indices = [
        Index(value = ["userId", "targetLanguage"]),
        Index(value = ["createdAt"])
    ]
)
@TypeConverters(PracticeConverters::class)
data class PracticeSessionEntity(
    @PrimaryKey val sessionId: String,
    val userId: String,
    val targetLanguage: String,
    val primaryTranslationId: String,
    val supplementaryTranslationIds: List<String>,
    val sessionType: SessionType,
    val totalItems: Int,
    val completedItems: Int,
    val averageScore: Float,
    val sessionDuration: Long, // in milliseconds
    val createdAt: Long,
    val completedAt: Long? = null,
    val isCompleted: Boolean = false
)

// Practice item entity - stores individual practice items
@Entity(
    tableName = "practice_items",
    indices = [
        Index(value = ["sourceTranslationId"]),
        Index(value = ["itemType", "difficultyLevel"]),
        Index(value = ["masteryScore"]),
        Index(value = ["lastPracticed"])
    ]
)
@TypeConverters(PracticeConverters::class)
data class PracticeItemEntity(
    @PrimaryKey val itemId: String,
    val content: String,
    val pronunciation: String?,
    val translations: List<String>,
    val sourceTranslationId: String,
    val itemType: PracticeItemType,
    val difficultyLevel: DifficultyLevel,
    val masteryScore: Float = 0f,
    val lastPracticed: Long? = null,
    val practiceCount: Int = 0,
    val isMarkedAsLearned: Boolean = false,
    val frequencyScore: Float = 0f,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

// Practice result entity - stores results for each item in a session
@Entity(
    tableName = "practice_results",
    foreignKeys = [
        ForeignKey(
            entity = PracticeSessionEntity::class,
            parentColumns = ["sessionId"],
            childColumns = ["sessionId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["sessionId"]),
        Index(value = ["itemId"]),
        Index(value = ["timestamp"])
    ]
)
@TypeConverters(PracticeConverters::class)
data class PracticeResultEntity(
    @PrimaryKey val resultId: String,
    val sessionId: String,
    val itemId: String,
    val itemType: PracticeItemType,
    val listeningScore: Float?,
    val pronunciationScore: Float?,
    val comprehensionScore: Float?,
    val attempts: Int,
    val timeSpent: Long, // in milliseconds
    val markedAsLearned: Boolean,
    val timestamp: Long = System.currentTimeMillis()
)

// User practice profile entity
@Entity(
    tableName = "user_practice_profiles",
    indices = [
        Index(value = ["userId", "targetLanguage"], unique = true)
    ]
)
@TypeConverters(PracticeConverters::class)
data class UserPracticeProfileEntity(
    @PrimaryKey val profileId: String,
    val userId: String,
    val targetLanguage: String,
    val totalSessions: Int = 0,
    val totalPracticeTime: Long = 0,
    val averageSessionScore: Float = 0f,
    val vocabularyMastery: Float = 0f,
    val pronunciationMastery: Float = 0f,
    val listeningMastery: Float = 0f,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val lastPracticeDate: Long? = null,
    val weakAreas: List<String> = emptyList(),
    val strongAreas: List<String> = emptyList(),
    val preferredDifficulty: DifficultyLevel = DifficultyLevel.BEGINNER,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

// Session analytics entity
@Entity(
    tableName = "session_analytics",
    foreignKeys = [
        ForeignKey(
            entity = PracticeSessionEntity::class,
            parentColumns = ["sessionId"],
            childColumns = ["sessionId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["sessionId"], unique = true)]
)
@TypeConverters(PracticeConverters::class)
data class SessionAnalyticsEntity(
    @PrimaryKey val analyticsId: String,
    val sessionId: String,
    val userId: String,
    val completionRate: Float,
    val averageScore: Float,
    val totalTimeSpent: Long,
    val itemsCompleted: Int,
    val itemsSkipped: Int,
    val itemsMarkedAsLearned: Int,
    val improvementAreas: List<String>,
    val achievements: List<String> = emptyList(),
    val createdAt: Long = System.currentTimeMillis()
)

// Item mastery tracking entity
@Entity(
    tableName = "item_mastery",
    indices = [
        Index(value = ["userId", "itemId"], unique = true),
        Index(value = ["masteryLevel"]),
        Index(value = ["lastPracticed"])
    ]
)
data class ItemMasteryEntity(
    @PrimaryKey val masteryId: String,
    val userId: String,
    val itemId: String,
    val itemContent: String,
    val itemType: PracticeItemType,
    val masteryLevel: Float = 0f, // 0.0 to 1.0
    val practiceCount: Int = 0,
    val successfulAttempts: Int = 0,
    val lastScore: Float = 0f,
    val lastPracticed: Long? = null,
    val isMarkedAsLearned: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

// Daily practice streak entity
@Entity(
    tableName = "practice_streaks",
    indices = [
        Index(value = ["userId", "targetLanguage"]),
        Index(value = ["practiceDate"])
    ]
)
data class PracticeStreakEntity(
    @PrimaryKey val streakId: String,
    val userId: String,
    val targetLanguage: String,
    val practiceDate: Long, // Date in milliseconds (start of day)
    val sessionsCompleted: Int,
    val totalPracticeTime: Long,
    val averageScore: Float,
    val createdAt: Long = System.currentTimeMillis()
)

// Achievement entity
@Entity(
    tableName = "practice_achievements",
    indices = [
        Index(value = ["userId"]),
        Index(value = ["achievementType"]),
        Index(value = ["unlockedAt"])
    ]
)
@TypeConverters(PracticeConverters::class)
data class PracticeAchievementEntity(
    @PrimaryKey val achievementId: String,
    val userId: String,
    val achievementType: AchievementType,
    val title: String,
    val description: String,
    val iconResource: String,
    val targetLanguage: String?,
    val progress: Float = 1.0f, // Achievement progress (1.0 = completed)
    val isUnlocked: Boolean = true,
    val unlockedAt: Long = System.currentTimeMillis()
)

@kotlinx.serialization.Serializable
enum class AchievementType {
    FIRST_SESSION,
    STREAK_3_DAYS,
    STREAK_7_DAYS,
    STREAK_30_DAYS,
    PERFECT_SESSION,
    PRONUNCIATION_MASTER,
    VOCABULARY_CHAMPION,
    LISTENING_EXPERT,
    FAST_LEARNER,
    PERSISTENT_LEARNER,
    MULTILINGUAL
}
