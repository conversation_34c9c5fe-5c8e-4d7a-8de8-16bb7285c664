package com.tfkcolin.practice.data.model

import androidx.room.Database
import androidx.room.RoomDatabase

@Database(
    entities = [
        Translation::class,
        WordStats::class,
        ExpressionStats::class
    ],
    version = 1,
    exportSchema = true
)
abstract class AppDatabase : RoomDatabase() {

    abstract fun translationDao(): TranslationDao

    abstract fun frequencyStatsDao(): FrequencyStatsDao

    companion object {
        const val DATABASE_NAME = "practice_app.db"
    }
}