package com.tfkcolin.practice.data.model

import androidx.room.Database
import androidx.room.RoomDatabase

@Database(
    entities = [
        Translation::class,
        WordStats::class,
        ExpressionStats::class,
        PracticeSessionEntity::class,
        PracticeItemEntity::class,
        PracticeResultEntity::class,
        UserPracticeProfileEntity::class,
        SessionAnalyticsEntity::class,
        ItemMasteryEntity::class,
        PracticeStreakEntity::class,
        PracticeAchievementEntity::class
    ],
    version = 2,
    exportSchema = true
)
abstract class AppDatabase : RoomDatabase() {

    abstract fun translationDao(): TranslationDao

    abstract fun frequencyStatsDao(): FrequencyStatsDao

    // Practice-related DAOs
    abstract fun practiceSessionDao(): PracticeSessionDao

    abstract fun practiceItemDao(): PracticeItemDao

    abstract fun practiceResultDao(): PracticeResultDao

    abstract fun userPracticeProfileDao(): UserPracticeProfileDao

    abstract fun itemMasteryDao(): ItemMasteryDao

    abstract fun practiceStreakDao(): PracticeStreakDao

    abstract fun practiceAchievementDao(): PracticeAchievementDao

    companion object {
        const val DATABASE_NAME = "practice_app.db"
    }
}