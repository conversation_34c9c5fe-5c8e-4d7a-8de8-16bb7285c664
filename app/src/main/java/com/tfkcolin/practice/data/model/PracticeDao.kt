package com.tfkcolin.practice.data.model

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface PracticeSessionDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: PracticeSessionEntity)
    
    @Update
    suspend fun updateSession(session: PracticeSessionEntity)
    
    @Query("SELECT * FROM practice_sessions WHERE sessionId = :sessionId")
    suspend fun getSessionById(sessionId: String): PracticeSessionEntity?
    
    @Query("SELECT * FROM practice_sessions WHERE userId = :userId AND targetLanguage = :targetLanguage ORDER BY createdAt DESC")
    fun getSessionsByUserAndLanguage(userId: String, targetLanguage: String): Flow<List<PracticeSessionEntity>>
    
    @Query("SELECT * FROM practice_sessions WHERE userId = :userId ORDER BY createdAt DESC LIMIT :limit")
    suspend fun getRecentSessions(userId: String, limit: Int = 10): List<PracticeSessionEntity>
    
    @Query("SELECT COUNT(*) FROM practice_sessions WHERE userId = :userId AND targetLanguage = :targetLanguage AND isCompleted = 1")
    suspend fun getCompletedSessionCount(userId: String, targetLanguage: String): Int
    
    @Query("SELECT AVG(averageScore) FROM practice_sessions WHERE userId = :userId AND targetLanguage = :targetLanguage AND isCompleted = 1")
    suspend fun getAverageSessionScore(userId: String, targetLanguage: String): Float?
    
    @Query("DELETE FROM practice_sessions WHERE sessionId = :sessionId")
    suspend fun deleteSession(sessionId: String)
}

@Dao
interface PracticeItemDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertItem(item: PracticeItemEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertItems(items: List<PracticeItemEntity>)
    
    @Update
    suspend fun updateItem(item: PracticeItemEntity)
    
    @Query("SELECT * FROM practice_items WHERE itemId = :itemId")
    suspend fun getItemById(itemId: String): PracticeItemEntity?
    
    @Query("SELECT * FROM practice_items WHERE sourceTranslationId = :translationId")
    suspend fun getItemsByTranslation(translationId: String): List<PracticeItemEntity>
    
    @Query("""
        SELECT * FROM practice_items 
        WHERE sourceTranslationId IN (
            SELECT id FROM translations 
            WHERE sourceLanguage = :targetLanguage
        )
        AND isMarkedAsLearned = 0
        ORDER BY masteryScore ASC, frequencyScore DESC, lastPracticed ASC
        LIMIT :limit
    """)
    suspend fun getItemsForPractice(targetLanguage: String, limit: Int): List<PracticeItemEntity>
    
    @Query("""
        SELECT * FROM practice_items 
        WHERE sourceTranslationId IN (
            SELECT id FROM translations 
            WHERE sourceLanguage = :targetLanguage
        )
        AND difficultyLevel = :difficulty
        AND isMarkedAsLearned = 0
        ORDER BY masteryScore ASC, frequencyScore DESC
        LIMIT :limit
    """)
    suspend fun getItemsByDifficulty(targetLanguage: String, difficulty: DifficultyLevel, limit: Int): List<PracticeItemEntity>
    
    @Query("UPDATE practice_items SET masteryScore = :score, lastPracticed = :timestamp, practiceCount = practiceCount + 1, updatedAt = :timestamp WHERE itemId = :itemId")
    suspend fun updateItemMastery(itemId: String, score: Float, timestamp: Long)
    
    @Query("UPDATE practice_items SET isMarkedAsLearned = :learned, updatedAt = :timestamp WHERE itemId = :itemId")
    suspend fun markItemAsLearned(itemId: String, learned: Boolean, timestamp: Long)
    
    @Query("SELECT COUNT(*) FROM practice_items WHERE sourceTranslationId IN (SELECT id FROM translations WHERE sourceLanguage = :targetLanguage) AND isMarkedAsLearned = 1")
    suspend fun getLearnedItemCount(targetLanguage: String): Int
    
    @Query("SELECT COUNT(*) FROM practice_items WHERE sourceTranslationId IN (SELECT id FROM translations WHERE sourceLanguage = :targetLanguage)")
    suspend fun getTotalItemCount(targetLanguage: String): Int
}

@Dao
interface PracticeResultDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertResult(result: PracticeResultEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertResults(results: List<PracticeResultEntity>)
    
    @Query("SELECT * FROM practice_results WHERE sessionId = :sessionId ORDER BY timestamp ASC")
    suspend fun getResultsBySession(sessionId: String): List<PracticeResultEntity>
    
    @Query("SELECT * FROM practice_results WHERE itemId = :itemId ORDER BY timestamp DESC")
    suspend fun getResultsByItem(itemId: String): List<PracticeResultEntity>
    
    @Query("""
        SELECT AVG(
            CASE 
                WHEN listeningScore IS NOT NULL AND pronunciationScore IS NOT NULL AND comprehensionScore IS NOT NULL 
                THEN (listeningScore + pronunciationScore + comprehensionScore) / 3.0
                WHEN listeningScore IS NOT NULL AND pronunciationScore IS NOT NULL 
                THEN (listeningScore + pronunciationScore) / 2.0
                WHEN listeningScore IS NOT NULL AND comprehensionScore IS NOT NULL 
                THEN (listeningScore + comprehensionScore) / 2.0
                WHEN pronunciationScore IS NOT NULL AND comprehensionScore IS NOT NULL 
                THEN (pronunciationScore + comprehensionScore) / 2.0
                WHEN listeningScore IS NOT NULL THEN listeningScore
                WHEN pronunciationScore IS NOT NULL THEN pronunciationScore
                WHEN comprehensionScore IS NOT NULL THEN comprehensionScore
                ELSE 0
            END
        ) FROM practice_results WHERE itemId = :itemId
    """)
    suspend fun getAverageScoreForItem(itemId: String): Float?
    
    @Query("DELETE FROM practice_results WHERE sessionId = :sessionId")
    suspend fun deleteResultsBySession(sessionId: String)
}

@Dao
interface UserPracticeProfileDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProfile(profile: UserPracticeProfileEntity)
    
    @Update
    suspend fun updateProfile(profile: UserPracticeProfileEntity)
    
    @Query("SELECT * FROM user_practice_profiles WHERE userId = :userId AND targetLanguage = :targetLanguage")
    suspend fun getProfile(userId: String, targetLanguage: String): UserPracticeProfileEntity?
    
    @Query("SELECT * FROM user_practice_profiles WHERE userId = :userId")
    suspend fun getAllProfilesForUser(userId: String): List<UserPracticeProfileEntity>
    
    @Query("UPDATE user_practice_profiles SET currentStreak = :streak, lastPracticeDate = :date, updatedAt = :timestamp WHERE userId = :userId AND targetLanguage = :targetLanguage")
    suspend fun updateStreak(userId: String, targetLanguage: String, streak: Int, date: Long, timestamp: Long)
    
    @Query("UPDATE user_practice_profiles SET totalSessions = totalSessions + 1, totalPracticeTime = totalPracticeTime + :sessionTime, averageSessionScore = :newAverage, updatedAt = :timestamp WHERE userId = :userId AND targetLanguage = :targetLanguage")
    suspend fun updateSessionStats(userId: String, targetLanguage: String, sessionTime: Long, newAverage: Float, timestamp: Long)
}

@Dao
interface ItemMasteryDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMastery(mastery: ItemMasteryEntity)
    
    @Update
    suspend fun updateMastery(mastery: ItemMasteryEntity)
    
    @Query("SELECT * FROM item_mastery WHERE userId = :userId AND itemId = :itemId")
    suspend fun getMastery(userId: String, itemId: String): ItemMasteryEntity?
    
    @Query("SELECT * FROM item_mastery WHERE userId = :userId AND masteryLevel < :threshold ORDER BY lastPracticed ASC")
    suspend fun getItemsNeedingPractice(userId: String, threshold: Float = 0.8f): List<ItemMasteryEntity>
    
    @Query("SELECT AVG(masteryLevel) FROM item_mastery WHERE userId = :userId")
    suspend fun getOverallMasteryLevel(userId: String): Float?
    
    @Query("UPDATE item_mastery SET masteryLevel = :level, practiceCount = practiceCount + 1, lastScore = :score, lastPracticed = :timestamp, updatedAt = :timestamp WHERE userId = :userId AND itemId = :itemId")
    suspend fun updateMasteryLevel(userId: String, itemId: String, level: Float, score: Float, timestamp: Long)
}

@Dao
interface PracticeStreakDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStreak(streak: PracticeStreakEntity)
    
    @Query("SELECT * FROM practice_streaks WHERE userId = :userId AND targetLanguage = :targetLanguage AND practiceDate = :date")
    suspend fun getStreakForDate(userId: String, targetLanguage: String, date: Long): PracticeStreakEntity?
    
    @Query("SELECT COUNT(*) FROM practice_streaks WHERE userId = :userId AND targetLanguage = :targetLanguage AND practiceDate >= :startDate ORDER BY practiceDate DESC")
    suspend fun getCurrentStreak(userId: String, targetLanguage: String, startDate: Long): Int
    
    @Query("SELECT MAX(streak_count) FROM (SELECT COUNT(*) as streak_count FROM practice_streaks WHERE userId = :userId AND targetLanguage = :targetLanguage GROUP BY (practiceDate - ROW_NUMBER() * 86400000))")
    suspend fun getLongestStreak(userId: String, targetLanguage: String): Int?
}

@Dao
interface PracticeAchievementDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievement(achievement: PracticeAchievementEntity)
    
    @Query("SELECT * FROM practice_achievements WHERE userId = :userId ORDER BY unlockedAt DESC")
    suspend fun getAchievementsForUser(userId: String): List<PracticeAchievementEntity>
    
    @Query("SELECT * FROM practice_achievements WHERE userId = :userId AND achievementType = :type")
    suspend fun getAchievementByType(userId: String, type: AchievementType): PracticeAchievementEntity?
    
    @Query("SELECT COUNT(*) FROM practice_achievements WHERE userId = :userId AND isUnlocked = 1")
    suspend fun getUnlockedAchievementCount(userId: String): Int
}
