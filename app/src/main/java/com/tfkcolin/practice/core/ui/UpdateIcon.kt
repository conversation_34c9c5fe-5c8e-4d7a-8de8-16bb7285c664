package com.tfkcolin.practice.core.ui

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SystemUpdate
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.tfkcolin.practice.core.InAppUpdateManager
import androidx.compose.ui.graphics.Color

@Composable
fun UpdateIcon(
    updateManager: InAppUpdateManager,
    onUpdateClick: () -> Unit
) {
    val updateAvailable by updateManager.updateAvailable.collectAsState()

    if (updateAvailable) {
        IconButton(onClick = onUpdateClick) {
            Icon(
                imageVector = Icons.Filled.SystemUpdate,
                contentDescription = "Update Available",
                tint = Color(0xFFFF9800) // Orange color to indicate update available
            )
        }
    }
}