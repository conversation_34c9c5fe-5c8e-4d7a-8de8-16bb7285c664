package com.tfkcolin.practice.core.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.practice.features.tts.domain.TtsUiState
import com.tfkcolin.practice.features.tts.domain.TtsViewModel

@Composable
fun EngineSelector(viewModel: TtsViewModel, uiState: TtsUiState) {
    val engines = mapOf(
        "android" to "System (Android)",
        "google" to "Google Cloud"
    )

    Column(modifier = Modifier.fillMaxWidth()) {
        Text("Select TTS Engine:")
        Spacer(modifier = Modifier.height(8.dp))

        engines.forEach { (key, name) ->
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = uiState.selectedEngine == key,
                    onClick = { viewModel.chooseEngine(key) },
                    enabled = !uiState.isInitializing
                )
                Text(text = name, modifier = androidx.compose.ui.Modifier.padding(start = 8.dp))
            }
        }
    }
}