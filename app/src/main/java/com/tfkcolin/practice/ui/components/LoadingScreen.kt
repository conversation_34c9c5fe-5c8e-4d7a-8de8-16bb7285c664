package com.tfkcolin.practice.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.practice.ui.theme.PrimaryBlack
import com.tfkcolin.practice.ui.theme.PrimaryWhite

/**
 * Loading screen displayed while the app is initializing (checking auth state, loading preferences, etc.)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoadingScreen(
    modifier: Modifier = Modifier,
    message: String = "Loading..."
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Circular progress indicator
        CircularProgressIndicator(
            modifier = Modifier.size(60.dp),
            color = MaterialTheme.colorScheme.primary,
            strokeWidth = 4.dp
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Loading message
        Text(
            text = "Language Practice",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = PrimaryBlack,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = PrimaryBlack.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Optional: Add app branding or version info
        Text(
            text = "Initializing your learning environment...",
            style = MaterialTheme.typography.bodyMedium,
            color = PrimaryBlack.copy(alpha = 0.5f),
            textAlign = TextAlign.Center
        )
    }
}