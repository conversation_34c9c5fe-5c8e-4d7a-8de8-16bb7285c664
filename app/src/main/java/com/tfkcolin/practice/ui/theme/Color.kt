package com.tfkcolin.practice.ui.theme

import androidx.compose.ui.graphics.Color

// Professional color palette for language learning app
// Dark mode colors (lighter for dark backgrounds)
val Teal80 = Color(0xFF4DD0E1)
val Orange80 = Color(0xFFFF9800)
val Purple80 = Color(0xFFBA68C8)
val Cyan80 = Color(0xFF4DD0E1) // Accent

// Light mode colors (darker for light backgrounds)
val Teal40 = Color(0xFF00BFA5)
val Orange40 = Color(0xFFFF6D00)
val Purple40 = Color(0xFF9C27B0)
val Cyan40 = Color(0xFF00BCD4) // Accent

// Additional colors for enhanced theming
val TealVariant = Color(0xFF009688)
val OrangeVariant = Color(0xFFE65100)
val PurpleVariant = Color(0xFF7B1FA2)

// Professional UI colors based on the design images
val PrimaryBlack = Color(0xFF000000)
val PrimaryWhite = Color(0xFFFFFFFF)
val LightGray = Color(0xFFF5F5F5)
val MediumGray = Color(0xFFE0E0E0)
val DarkGray = Color(0xFF757575)
val TextSecondary = Color(0xFF666666)
val GoogleBlue = Color(0xFF4285F4)
val AppleBlack = Color(0xFF000000)
val SuccessGreen = Color(0xFF4CAF50)
val ErrorRed = Color(0xFFF44336)
val AccentBlue = Color(0xFF2196F3)
val AccentGreen = Color(0xFF4CAF50)
val AccentOrange = Color(0xFFFF9800)
val AccentPurple = Color(0xFF9C27B0)
val AccentYellow = Color(0xFFFFC107)
val BackgroundGray = Color(0xFFF8F9FA)