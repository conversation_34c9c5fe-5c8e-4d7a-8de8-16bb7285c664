package com.tfkcolin.practice

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.activity.ComponentActivity
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import com.tfkcolin.practice.features.share.domain.ShareProcessingViewModel
import com.tfkcolin.practice.features.share.ui.ShareProcessingScreen
import com.tfkcolin.practice.navigation.Route
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private const val TAG = "ShareActivity"

@AndroidEntryPoint
class ShareActivity : ComponentActivity() {

    private val shareProcessingViewModel: ShareProcessingViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, false)

        Log.d(TAG, "ShareActivity created with intent: ${intent.action}")

        try {
            // Process the shared content
            handleSharedIntent(intent)

            // Set the content view
            setContentView(
                androidx.compose.ui.platform.ComposeView(this).apply {
                    setContent {
                        ShareProcessingScreen(
                            viewModel = shareProcessingViewModel,
                            onProcessingComplete = { translationId ->
                                navigateToTranslationResults(translationId)
                            },
                            onError = {
                                // Show error and close
                                finish()
                            }
                        )
                    }
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Fatal error in ShareActivity.onCreate", e)
            // Show error and close
            finish()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "ShareActivity received new intent: ${intent.action}")
        handleSharedIntent(intent)
    }

    private fun handleSharedIntent(intent: Intent) {
        try {
            when (intent.action) {
                Intent.ACTION_SEND -> {
                    handleSendIntent(intent)
                }
                Intent.ACTION_SEND_MULTIPLE -> {
                    handleSendMultipleIntent(intent)
                }
                else -> {
                    Log.w(TAG, "Unsupported intent action: ${intent.action}")
                    showUnsupportedFormatError()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling shared intent", e)
            showUnsupportedFormatError()
        }
    }

    private fun handleSendIntent(intent: Intent) {
        try {
            val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
            val sharedTitle = intent.getStringExtra(Intent.EXTRA_TITLE)
            val sharedUri: Uri? = intent.getParcelableExtra(Intent.EXTRA_STREAM)
            val mimeType = intent.type

            Log.d(TAG, "Shared content - Text: ${sharedText?.take(50)}, URI: $sharedUri, MIME: $mimeType")

            when {
                sharedText != null -> {
                    // Handle shared text
                    shareProcessingViewModel.processSharedText(sharedText, sharedTitle)
                }
                sharedUri != null && mimeType != null -> {
                    // Handle shared file
                    shareProcessingViewModel.processSharedFile(sharedUri, mimeType)
                }
                else -> {
                    Log.w(TAG, "No valid shared content found")
                    showUnsupportedFormatError()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling SEND intent", e)
            showUnsupportedFormatError()
        }
    }

    private fun handleSendMultipleIntent(intent: Intent) {
        try {
            val sharedUris: ArrayList<Uri>? = intent.getParcelableArrayListExtra(Intent.EXTRA_STREAM)
            val mimeType = intent.type

            Log.d(TAG, "Shared multiple files - Count: ${sharedUris?.size}, MIME: $mimeType")

            if (sharedUris.isNullOrEmpty()) {
                Log.w(TAG, "No URIs found in SEND_MULTIPLE intent")
                showUnsupportedFormatError()
                return
            }

            shareProcessingViewModel.processSharedFiles(sharedUris, mimeType ?: "*/*")
        } catch (e: Exception) {
            Log.e(TAG, "Error handling SEND_MULTIPLE intent", e)
            showUnsupportedFormatError()
        }
    }

    private fun navigateToTranslationResults(translationId: String) {
        lifecycleScope.launch {
            // Small delay to ensure UI updates are processed
            delay(500)

            val intent = Intent(this@ShareActivity, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("navigation_route", Route.TranslationResults.createRoute(translationId))
            }
            startActivity(intent)
            finish()
        }
    }

    private fun showUnsupportedFormatError() {
        lifecycleScope.launch {
            shareProcessingViewModel.setError("Unsupported content type or format. Please share text, images, audio files, or documents (.txt, .md, .pdf).")
            delay(3000) // Show error for 3 seconds
            finish()
        }
    }
}