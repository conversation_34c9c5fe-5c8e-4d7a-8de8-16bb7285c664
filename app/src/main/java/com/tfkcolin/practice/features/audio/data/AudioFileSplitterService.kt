package com.tfkcolin.practice.features.audio.data

import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMuxer
import android.util.Log
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for splitting audio files into smaller segments
 */
interface AudioFileSplitterService {

    /**
     * Split an audio file into multiple segments
     * @param audioFile The source audio file
     * @param segmentDurationMs Duration of each segment in milliseconds
     * @param overlapMs Overlap between segments in milliseconds
     * @param outputDir Directory to store the segments
     * @return Result containing list of segment files or error
     */
    fun splitAudioFile(
        audioFile: File,
        segmentDurationMs: Long = SEGMENT_DURATION_MS,
        overlapMs: Long = SEGMENT_OVERLAP_MS,
        outputDir: File = audioFile.parentFile ?: File(".")
    ): Result<List<File>>

    /**
     * Clean up temporary segment files
     * @param segmentFiles List of segment files to delete
     */
    fun cleanupSegments(segmentFiles: List<File>)

    companion object {
        const val SEGMENT_DURATION_MS = 5 * 60 * 1000L // 5 minutes per segment
        const val SEGMENT_OVERLAP_MS = 30 * 1000L // 30 seconds overlap between segments
        const val MAX_SEGMENTS = 20 // Safety limit to prevent excessive splitting
    }
}

data class AudioSegment(
    val file: File,
    val startTimeMs: Long,
    val endTimeMs: Long,
    val index: Int
)

@Singleton
class AndroidAudioFileSplitterService @Inject constructor(
    private val metadataService: AudioMetadataService
) : AudioFileSplitterService {

    companion object {
        private const val TAG = "AudioFileSplitterService"
    }

    override fun splitAudioFile(
        audioFile: File,
        segmentDurationMs: Long,
        overlapMs: Long,
        outputDir: File
    ): Result<List<File>> {
        return try {
            // Extract metadata first
            val metadataResult = metadataService.extractMetadata(audioFile)
            if (metadataResult.isFailure) {
                return Result.failure(metadataResult.exceptionOrNull()!!)
            }

            val metadata = metadataResult.getOrNull()!!
            val segmentCount = metadataService.calculateSegmentCount(metadata, segmentDurationMs)

            if (segmentCount <= 1) {
                Log.d(TAG, "Audio file is short enough, no splitting needed")
                return Result.success(listOf(audioFile))
            }

            if (segmentCount > AudioFileSplitterService.MAX_SEGMENTS) {
                return Result.failure(
                    IOException("Audio file too long. Would require $segmentCount segments (max: ${AudioFileSplitterService.MAX_SEGMENTS})")
                )
            }

            // Ensure output directory exists
            if (!outputDir.exists() && !outputDir.mkdirs()) {
                return Result.failure(IOException("Failed to create output directory: ${outputDir.absolutePath}"))
            }

            val segments = mutableListOf<File>()
            val extractor = MediaExtractor()

            try {
                extractor.setDataSource(audioFile.absolutePath)
                val trackIndex = selectAudioTrack(extractor)

                if (trackIndex == -1) {
                    return Result.failure(IOException("No audio track found in file"))
                }

                extractor.selectTrack(trackIndex)
                val format = extractor.getTrackFormat(trackIndex)

                // Split into segments
                for (i in 0 until segmentCount) {
                    val segmentStartTime = i * (segmentDurationMs - overlapMs)
                    val segmentEndTime = minOf(
                        (i + 1) * segmentDurationMs,
                        metadata.durationMs
                    )

                    val segmentFile = createSegmentFile(audioFile, outputDir, i)
                    val segmentResult = extractSegment(
                        extractor,
                        format,
                        segmentFile,
                        segmentStartTime,
                        segmentEndTime
                    )

                    if (segmentResult.isSuccess) {
                        segments.add(segmentFile)
                        Log.d(TAG, "Created segment ${i + 1}/$segmentCount: ${segmentFile.name}")
                    } else {
                        // Clean up any segments created so far
                        cleanupSegments(segments)
                        return Result.failure(segmentResult.exceptionOrNull()!!)
                    }

                    // Reset extractor position for next segment
                    extractor.seekTo(segmentStartTime * 1000, MediaExtractor.SEEK_TO_CLOSEST_SYNC)
                }

                Result.success(segments)

            } finally {
                extractor.release()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error splitting audio file ${audioFile.name}", e)
            Result.failure(IOException("Failed to split audio file: ${e.message}", e))
        }
    }

    private fun selectAudioTrack(extractor: MediaExtractor): Int {
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME)
            if (mime?.startsWith("audio/") == true) {
                return i
            }
        }
        return -1
    }

    private fun createSegmentFile(sourceFile: File, outputDir: File, index: Int): File {
        val baseName = sourceFile.nameWithoutExtension
        val extension = sourceFile.extension
        return File(outputDir, "${baseName}_segment_${index + 1}.$extension")
    }

    private fun extractSegment(
        extractor: MediaExtractor,
        format: MediaFormat,
        outputFile: File,
        startTimeUs: Long,
        endTimeUs: Long
    ): Result<Unit> {
        val muxer = MediaMuxer(outputFile.absolutePath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)

        return try {
            val trackIndex = muxer.addTrack(format)
            muxer.start()

            val buffer = ByteArray(1024 * 1024) // 1MB buffer
            var bufferInfo = android.media.MediaCodec.BufferInfo()
            var isCompleted = false

            // Seek to start position
            extractor.seekTo(startTimeUs * 1000, MediaExtractor.SEEK_TO_CLOSEST_SYNC)

            while (!isCompleted) {
                bufferInfo = android.media.MediaCodec.BufferInfo()
                val byteBuffer = java.nio.ByteBuffer.wrap(buffer)
                val sampleSize = extractor.readSampleData(byteBuffer, 0)

                if (sampleSize < 0) {
                    isCompleted = true
                    break
                }

                val sampleTime = extractor.sampleTime
                if (sampleTime > endTimeUs * 1000) {
                    isCompleted = true
                    break
                }

                bufferInfo.presentationTimeUs = sampleTime
                bufferInfo.size = sampleSize
                // Map MediaExtractor sample flags to MediaCodec buffer flags
                bufferInfo.flags = when (extractor.sampleFlags) {
                    MediaExtractor.SAMPLE_FLAG_SYNC -> android.media.MediaCodec.BUFFER_FLAG_SYNC_FRAME
                    MediaExtractor.SAMPLE_FLAG_ENCRYPTED -> 0 // No equivalent in MediaCodec
                    else -> 0
                }

                muxer.writeSampleData(trackIndex, java.nio.ByteBuffer.wrap(buffer), bufferInfo)
                extractor.advance()
            }

            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "Error extracting segment to ${outputFile.name}", e)
            Result.failure(IOException("Failed to extract segment: ${e.message}", e))
        } finally {
            try {
                muxer.stop()
                muxer.release()
            } catch (e: Exception) {
                Log.w(TAG, "Error releasing muxer", e)
            }
        }
    }

    override fun cleanupSegments(segmentFiles: List<File>) {
        segmentFiles.forEach { file ->
            try {
                if (file.exists() && file.delete()) {
                    Log.d(TAG, "Cleaned up segment file: ${file.name}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to delete segment file: ${file.name}", e)
            }
        }
    }
}