package com.tfkcolin.practice.features.tts.data

import com.tfkcolin.practice.data.engine.TtsEngine
import java.util.Locale

class TtsRepository(private val engine: Map<String, TtsEngine>) {
    private var currentEngine: TtsEngine? = null
    fun chooseEngine(key: String, callback: (Boolean) -> Unit){
        currentEngine?.shutdown()
        currentEngine = engine[key]
        currentEngine?.init { success ->
            if (!success) {
                currentEngine = null
            }
            callback(success)
        } ?: callback(false)
    }

    fun speak(text: String) = currentEngine?.speak(text)

    fun shutdown() = currentEngine?.shutdown()

    fun setLanguage(locale: Locale) = currentEngine?.setLanguage(locale)

    fun getAvailableLanguages(): List<Locale> = currentEngine?.getAvailableLanguages() ?: emptyList()
}