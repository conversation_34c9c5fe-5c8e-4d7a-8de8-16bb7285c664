package com.tfkcolin.practice.features.audio.domain

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.mlkit.nl.languageid.LanguageIdentification
import com.google.mlkit.nl.languageid.LanguageIdentifier
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.audio.data.AudioTranscriptionProcessor
import com.tfkcolin.practice.features.audio.data.AudioProcessingProgress
import com.tfkcolin.practice.features.audio.data.ProcessingStage
import com.tfkcolin.practice.features.language.domain.LanguageManager
import com.tfkcolin.practice.features.text.data.ProcessingResult
import com.tfkcolin.practice.features.text.data.TextProcessingPipeline
import com.tfkcolin.practice.features.audio.data.AudioTranscriptionService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class AudioPracticeViewModel @Inject constructor(
    private val languageManager: LanguageManager,
    private val transcriptionProcessor: AudioTranscriptionProcessor,
    private val transcriptionService: AudioTranscriptionService, // Keep for validation
    private val textProcessingPipeline: TextProcessingPipeline,
    private val preferencesRepository: PreferencesRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _uiState = MutableStateFlow(AudioPracticeUiState())
    val uiState: StateFlow<AudioPracticeUiState> = _uiState

    private var currentTranslationId: String? = null

    fun selectAudioFile(file: File) {
        val isValid = validateAudioFile(file)
        _uiState.value = _uiState.value.copy(
            selectedFile = file,
            isFileValid = isValid,
            fileError = if (isValid) null else "Unsupported file format",
            transcription = "",
            isTranscribing = false,
            transcriptionError = null,
            processingProgress = null,
            isProcessingText = false,
            processingError = null,
            isProcessingComplete = false,
            processedTranslation = null
        )
        currentTranslationId = null
    }

    private fun validateAudioFile(file: File): Boolean {
        val extension = file.extension.lowercase()
        return transcriptionService.getSupportedFormats().contains(extension)
    }

    fun transcribeSelectedFile() {
        val file = _uiState.value.selectedFile ?: return

        _uiState.value = _uiState.value.copy(
            isTranscribing = true,
            transcriptionError = null,
            processingProgress = null
        )

        transcriptionProcessor.processAudioFile(
            audioFile = file,
            progressCallback = { progress ->
                onProcessingProgress(progress)
            },
            callback = { result ->
                result.fold(
                    onSuccess = { transcription ->
                        onTranscriptionSuccess(transcription)
                    },
                    onFailure = { error ->
                        onTranscriptionError(error.message ?: "Processing failed")
                    }
                )
            }
        )
    }

    private fun onProcessingProgress(progress: AudioProcessingProgress) {
        _uiState.value = _uiState.value.copy(
            processingProgress = progress,
            isTranscribing = progress.stage != ProcessingStage.COMPLETED,
            transcriptionError = if (progress.stage == ProcessingStage.ERROR) progress.message else null
        )
    }

    private fun onTranscriptionSuccess(transcription: String) {
        _uiState.value = _uiState.value.copy(
            transcription = transcription,
            isTranscribing = false,
            showProceedButton = true,
            processingError = null
        )
    }


    private fun onTranscriptionError(error: String) {
        _uiState.value = _uiState.value.copy(
            isTranscribing = false,
            transcriptionError = error
        )
    }

    fun getCurrentTranslationId(): String? {
        return currentTranslationId
    }

    fun clearErrors() {
        _uiState.value = _uiState.value.copy(
            fileError = null,
            transcriptionError = null,
            processingError = null,
            processingProgress = null
        )
    }

    /**
     * Get all available recording files from the app's recordings directory
     */
    fun getRecordingFiles(): List<File> {
        val recordingsDir = File(context.filesDir, "recordings")
        return if (recordingsDir.exists()) {
            recordingsDir.walk()
                .filter { it.isFile && it.extension in transcriptionService.getSupportedFormats() }
                .sortedByDescending { it.lastModified() }
                .toList()
        } else {
            emptyList()
        }
    }
}