package com.tfkcolin.practice.features.speech.data

import com.tfkcolin.practice.data.engine.SpeechEngine
import java.util.Locale

class SpeechRepository(private val engines: Map<String, SpeechEngine>) {
    private var currentEngine: SpeechEngine? = null

    fun chooseEngine(key: String, callback: (Boolean) -> Unit) {
        currentEngine?.shutdown()
        currentEngine = engines[key]
        currentEngine?.init { success ->
            if (!success) {
                currentEngine = null
            }
            callback(success)
        } ?: callback(false)
    }

    fun startListening(callback: (String) -> Unit, errorCallback: (String) -> Unit) {
        currentEngine?.startListening(callback, errorCallback)
    }

    fun stopListening() = currentEngine?.stopListening()

    fun shutdown() = currentEngine?.shutdown()

    fun setLanguage(locale: Locale) = currentEngine?.setLanguage(locale)

    fun getAvailableLanguages(): List<Locale> = currentEngine?.getAvailableLanguages() ?: emptyList()

    val isListening: Boolean get() = currentEngine?.isListening ?: false
}