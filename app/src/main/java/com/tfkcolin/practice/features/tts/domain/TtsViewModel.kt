package com.tfkcolin.practice.features.tts.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.tts.data.TtsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class TtsViewModel @Inject constructor(
    private val ttsRepository: TtsRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(TtsUiState())
    val uiState: StateFlow<TtsUiState> = _uiState

    init {
        chooseEngine("android") // default
    }

    fun onSearchQueryChange(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
    }

    fun onToggleSearch() {
        _uiState.value = _uiState.value.copy(isSearching = !_uiState.value.isSearching)
    }

    fun updateText(text: String) {
        _uiState.value = _uiState.value.copy(inputText = text)
    }

    fun chooseEngine(engineKey: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isInitializing = true)
            ttsRepository.chooseEngine(engineKey) { success ->
                val languages = if (success) ttsRepository.getAvailableLanguages() else emptyList()
                val currentLocale = _uiState.value.selectedLanguage
                val selectedLocale = languages.firstOrNull() ?: currentLocale
                _uiState.value = _uiState.value.copy(
                    isInitializing = false,
                    selectedEngine = engineKey,
                    isEngineReady = success,
                    statusMessage = if (success) "Engine ready" else "Failed to initialize engine",
                    availableLanguages = languages,
                    selectedLanguage = selectedLocale
                )
                if (success) {
                    setSelectedLanguage(selectedLocale)
                }
            }
        }
    }


    fun queryAvailableLanguages() {
        val languages = ttsRepository.getAvailableLanguages()
        _uiState.value = _uiState.value.copy(availableLanguages = languages)
    }

    fun setSelectedLanguage(locale: Locale) {
        ttsRepository.setLanguage(locale)
        _uiState.value = _uiState.value.copy(selectedLanguage = locale)
    }

    fun speak() {
        val text = _uiState.value.inputText
        if (text.isNotBlank() && _uiState.value.isEngineReady) {
            ttsRepository.speak(text)
        } else {
            _uiState.value = _uiState.value.copy(statusMessage = "Text is empty or engine not ready")
        }
    }

    override fun onCleared() {
        ttsRepository.shutdown()
        super.onCleared()
    }
}