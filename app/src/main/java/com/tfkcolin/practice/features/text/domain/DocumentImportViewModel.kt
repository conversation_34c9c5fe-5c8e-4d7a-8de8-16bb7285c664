package com.tfkcolin.practice.features.text.domain

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.text.data.DocumentTextProcessingService
import com.tfkcolin.practice.features.text.data.ProcessingResult
import com.tfkcolin.practice.features.text.data.ProcessingStep
import com.tfkcolin.practice.features.text.data.TextProcessingPipeline
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import javax.inject.Inject
import kotlin.system.measureTimeMillis

data class ChunkResult(
    val index: Int,
    val translation: Translation?,
    val error: Exception?
)

data class DocumentImportUiState(
    val isLoading: Boolean = false,
    val selectedDocumentUri: Uri? = null,
    val extractedText: String? = null,
    val processingStep: ProcessingStep = ProcessingStep.IDLE,
    val processedChunks: Int = 0,
    val totalChunks: Int = 0,
    val error: String? = null
)

@HiltViewModel
class DocumentImportViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val documentTextProcessingService: DocumentTextProcessingService,
    private val textProcessingPipeline: TextProcessingPipeline,
    private val preferencesRepository: PreferencesRepository
) : ViewModel() {

    companion object {
        private const val TAG = "DocumentImportViewModel"
        private const val MAX_PARALLEL_CHUNKS = 3 // Process up to 3 chunks concurrently
    }

    private val _uiState = MutableStateFlow(DocumentImportUiState())
    val uiState: StateFlow<DocumentImportUiState> = _uiState.asStateFlow()

    fun onDocumentSelected(uri: Uri) {
        Log.d(TAG, "Document selected: $uri")
        _uiState.value = _uiState.value.copy(
            selectedDocumentUri = uri,
            error = null,
            isLoading = true,
            processingStep = ProcessingStep.IDLE,
            processedChunks = 0,
            totalChunks = 0
        )
        Log.d(TAG, "UI state updated to loading, starting text extraction")
        extractTextFromDocument(uri)
    }

    fun processDocumentForTranslation(
        onSuccess: (Translation) -> Unit,
        onError: (Exception) -> Unit
    ) {
        val extractedText = _uiState.value.extractedText ?: return
        Log.d(TAG, "Starting document processing for text length: ${extractedText.length}")

        // Immediately set loading state to show progress to user
        _uiState.value = _uiState.value.copy(
            isLoading = true,
            processingStep = ProcessingStep.DETECTING_LANGUAGE,
            processedChunks = 0,
            totalChunks = 1 // Will be updated once we split the text
        )

        val userPrefs = preferencesRepository.getCurrentPreferences()
        val targetLanguage = userPrefs.selectedTargetLanguage

        viewModelScope.launch {
            try {
                val totalStartTime = System.currentTimeMillis()
                Log.d(TAG, "Splitting text into paragraphs")
                val splitStartTime = System.currentTimeMillis()
                val chunks = documentTextProcessingService.splitIntoParagraphs(extractedText)
                val splitTime = System.currentTimeMillis() - splitStartTime
                Log.d(TAG, "Split into ${chunks.size} chunks in ${splitTime}ms")

                _uiState.value = _uiState.value.copy(
                    totalChunks = chunks.size,
                    processedChunks = 0,
                    processingStep = ProcessingStep.DETECTING_LANGUAGE,
                    isLoading = true
                )
                Log.d(TAG, "UI state updated to start processing")

                var lastTranslation: Translation? = null
                var processedCount = 0

                // Process chunks in parallel with limited concurrency
                val deferredResults = chunks.mapIndexedNotNull { index, chunk ->
                    if (chunk.isBlank()) {
                        Log.d(TAG, "Skipping blank chunk at index $index")
                        null
                    } else {
                        async {
                            Log.d(TAG, "Starting parallel processing for chunk ${index + 1}/${chunks.size}, length: ${chunk.length}")
                            val chunkStartTime = System.currentTimeMillis()

                            try {
                                var chunkTranslation: Translation? = null
                                var chunkError: Exception? = null

                                // Process the chunk and collect the result
                                textProcessingPipeline.processText(
                                    text = chunk,
                                    source = "document",
                                    targetLanguage = targetLanguage
                                ).collect { result ->
                                    when (result) {
                                        is ProcessingResult.Progress -> {
                                            // Update UI state with current progress
                                            _uiState.value = _uiState.value.copy(
                                                processingStep = result.step,
                                                isLoading = true
                                            )
                                        }
                                        is ProcessingResult.Success -> {
                                            val chunkTime = System.currentTimeMillis() - chunkStartTime
                                            Log.d(TAG, "Processing success for chunk ${index + 1} in ${chunkTime}ms")
                                            chunkTranslation = result.translation
                                            return@collect
                                        }
                                        is ProcessingResult.Error -> {
                                            val chunkTime = System.currentTimeMillis() - chunkStartTime
                                            Log.e(TAG, "Processing error for chunk ${index + 1} in ${chunkTime}ms", result.exception)
                                            chunkError = result.exception
                                            return@collect
                                        }
                                    }
                                }

                                // Update progress counter atomically
                                synchronized(this@DocumentImportViewModel) {
                                    processedCount++
                                    _uiState.value = _uiState.value.copy(
                                        processedChunks = processedCount,
                                        processingStep = if (processedCount < chunks.size) ProcessingStep.PROCESSING_AI else ProcessingStep.COMPLETED,
                                        isLoading = processedCount < chunks.size
                                    )
                                }

                                // Return result for this chunk
                                ChunkResult(index, chunkTranslation, chunkError)

                            } catch (e: Exception) {
                                val chunkTime = System.currentTimeMillis() - chunkStartTime
                                Log.e(TAG, "Exception processing chunk ${index + 1} in ${chunkTime}ms", e)

                                synchronized(this@DocumentImportViewModel) {
                                    processedCount++
                                    _uiState.value = _uiState.value.copy(
                                        processedChunks = processedCount,
                                        isLoading = processedCount < chunks.size
                                    )
                                }

                                ChunkResult(index, null, e)
                            }
                        }
                    }
                }

                // Wait for all parallel tasks to complete
                val chunkResults = deferredResults.map { it.await() }

                // Process results - use the last successful translation
                val successfulTranslations = chunkResults.filter { it.translation != null }
                if (successfulTranslations.isNotEmpty()) {
                    lastTranslation = successfulTranslations.last().translation
                }

                // Log any errors that occurred
                val failedChunks = chunkResults.filter { it.error != null }
                if (failedChunks.isNotEmpty()) {
                    Log.w(TAG, "${failedChunks.size} chunks failed processing: ${failedChunks.joinToString { "Chunk ${it.index + 1}: ${it.error?.message}" }}")
                }

                val totalTime = System.currentTimeMillis() - totalStartTime
                Log.d(TAG, "Processing complete. Processed $processedCount chunks in ${totalTime}ms. Last translation: ${lastTranslation != null}")
                val finalTranslation = lastTranslation
                if (finalTranslation != null) {
                    Log.d(TAG, "Calling onSuccess with translation")
                    // Clear loading state on success
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        processingStep = ProcessingStep.COMPLETED
                    )
                    onSuccess(finalTranslation)
                } else {
                    val error = Exception("No chunks were successfully processed")
                    Log.e(TAG, "No chunks processed successfully", error)
                    // Clear loading state on error
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        processingStep = ProcessingStep.ERROR,
                        error = "Processing failed: ${error.localizedMessage}"
                    )
                    onError(error)
                }

            } catch (e: Exception) {
                Log.e(TAG, "Exception during document processing", e)
                // Clear loading state on exception
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    processingStep = ProcessingStep.ERROR,
                    error = "Processing failed: ${e.localizedMessage}"
                )
                onError(e)
            }
        }
    }

    private fun extractTextFromDocument(uri: Uri) {
        Log.d(TAG, "Starting text extraction for URI: $uri")
        viewModelScope.launch {
            try {
                // First check if the file type is supported
                val mimeType = context.contentResolver.getType(uri)
                Log.d(TAG, "Detected MIME type: $mimeType")

                if (!documentTextProcessingService.isSupportedMimeType(mimeType)) {
                    Log.w(TAG, "Unsupported MIME type: $mimeType")
                    _uiState.value = _uiState.value.copy(
                        error = "Unsupported file type. Detected: $mimeType. Please select a .txt or .md file.",
                        isLoading = false
                    )
                    return@launch
                }

                Log.d(TAG, "MIME type supported, calling extractTextFromDocument")
                val result = documentTextProcessingService.extractTextFromDocument(context, uri)
                result.fold(
                    onSuccess = { text ->
                        Log.d(TAG, "Text extraction successful, length: ${text.length}")
                        if (text.isNotBlank()) {
                            Log.d(TAG, "Setting extracted text in UI state")
                            _uiState.value = _uiState.value.copy(
                                extractedText = text,
                                isLoading = false
                            )
                        } else {
                            Log.w(TAG, "Extracted text is blank")
                            _uiState.value = _uiState.value.copy(
                                error = "The selected document appears to be empty.",
                                isLoading = false
                            )
                        }
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "Failed to extract text from document", exception)
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to read document: ${exception.localizedMessage}",
                            isLoading = false
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error during text extraction", e)
                _uiState.value = _uiState.value.copy(
                    error = "Unexpected error: ${e.localizedMessage}",
                    isLoading = false
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun reset() {
        _uiState.value = DocumentImportUiState()
    }
}
