package com.tfkcolin.practice.features.practice.domain

import com.tfkcolin.practice.data.model.*
import com.tfkcolin.practice.features.speech.repository.SpeechRepository
import com.tfkcolin.practice.features.tts.repository.TtsRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

@Singleton
class PronunciationAnalyzer @Inject constructor(
    private val speechRepository: SpeechRepository,
    private val ttsRepository: TtsRepository
) {

    suspend fun analyzePronunciation(
        targetText: String,
        userTranscription: String,
        targetLanguage: String
    ): PronunciationAnalysis = withContext(Dispatchers.Default) {
        
        // 1. Normalize both texts for comparison
        val normalizedTarget = normalizeText(targetText)
        val normalizedUser = normalizeText(userTranscription)
        
        // 2. Calculate overall similarity score
        val overallScore = calculateSimilarityScore(normalizedTarget, normalizedUser)
        
        // 3. Perform phoneme-level analysis
        val phonemeComparisons = comparePhonemes(normalizedTarget, normalizedUser)
        
        // 4. Generate improvement suggestions
        val suggestions = generateImprovementSuggestions(
            targetText = normalizedTarget,
            userText = normalizedUser,
            phonemeComparisons = phonemeComparisons,
            overallScore = overallScore
        )
        
        PronunciationAnalysis(
            targetText = targetText,
            userTranscription = userTranscription,
            overallScore = overallScore,
            phonemeScores = phonemeComparisons,
            suggestions = suggestions,
            canPlayUserAudio = false, // User audio playback not implemented yet
            canPlayReferenceAudio = true
        )
    }
    
    private fun normalizeText(text: String): String {
        return text.lowercase()
            .replace(Regex("[^\\p{L}\\s]"), "") // Remove non-letter, non-space characters
            .replace(Regex("\\s+"), " ") // Normalize whitespace
            .trim()
    }
    
    private fun calculateSimilarityScore(target: String, user: String): Float {
        if (target.isEmpty() && user.isEmpty()) return 1.0f
        if (target.isEmpty() || user.isEmpty()) return 0.0f
        
        // Use Levenshtein distance for basic similarity
        val distance = levenshteinDistance(target, user)
        val maxLength = max(target.length, user.length)
        
        // Convert distance to similarity score (0.0 to 1.0)
        val similarity = 1.0f - (distance.toFloat() / maxLength)
        
        // Apply additional scoring based on word-level accuracy
        val wordAccuracy = calculateWordAccuracy(target, user)
        
        // Combine character-level and word-level scores
        return (similarity * 0.6f + wordAccuracy * 0.4f).coerceIn(0.0f, 1.0f)
    }
    
    private fun calculateWordAccuracy(target: String, user: String): Float {
        val targetWords = target.split("\\s+".toRegex())
        val userWords = user.split("\\s+".toRegex())
        
        if (targetWords.isEmpty()) return 1.0f
        
        var correctWords = 0
        val minLength = min(targetWords.size, userWords.size)
        
        for (i in 0 until minLength) {
            if (targetWords[i] == userWords[i]) {
                correctWords++
            } else if (isSimilarWord(targetWords[i], userWords[i])) {
                correctWords++ // Give partial credit for similar words
            }
        }
        
        return correctWords.toFloat() / targetWords.size
    }
    
    private fun isSimilarWord(target: String, user: String): Boolean {
        if (target.length <= 2 || user.length <= 2) return false
        
        val distance = levenshteinDistance(target, user)
        val maxLength = max(target.length, user.length)
        
        // Consider words similar if they differ by at most 1 character per 3 characters
        return distance <= maxLength / 3
    }
    
    private fun levenshteinDistance(s1: String, s2: String): Int {
        val len1 = s1.length
        val len2 = s2.length
        
        val dp = Array(len1 + 1) { IntArray(len2 + 1) }
        
        for (i in 0..len1) dp[i][0] = i
        for (j in 0..len2) dp[0][j] = j
        
        for (i in 1..len1) {
            for (j in 1..len2) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }
        
        return dp[len1][len2]
    }
    
    private fun comparePhonemes(target: String, user: String): List<PhonemeComparison> {
        val targetWords = target.split("\\s+".toRegex())
        val userWords = user.split("\\s+".toRegex())
        val comparisons = mutableListOf<PhonemeComparison>()
        
        val maxWords = max(targetWords.size, userWords.size)
        
        for (i in 0 until maxWords) {
            val targetWord = targetWords.getOrNull(i) ?: ""
            val userWord = userWords.getOrNull(i) ?: ""
            
            if (targetWord.isNotEmpty()) {
                val accuracy = if (userWord.isEmpty()) {
                    0.0f
                } else {
                    calculateWordSimilarity(targetWord, userWord)
                }
                
                val feedback = generatePhonemeFeeback(targetWord, userWord, accuracy)
                
                comparisons.add(
                    PhonemeComparison(
                        phoneme = targetWord,
                        accuracy = accuracy,
                        feedback = feedback
                    )
                )
            }
        }
        
        return comparisons
    }
    
    private fun calculateWordSimilarity(target: String, user: String): Float {
        if (target == user) return 1.0f
        if (target.isEmpty() || user.isEmpty()) return 0.0f
        
        val distance = levenshteinDistance(target, user)
        val maxLength = max(target.length, user.length)
        
        return (1.0f - distance.toFloat() / maxLength).coerceIn(0.0f, 1.0f)
    }
    
    private fun generatePhonemeFeeback(target: String, user: String, accuracy: Float): String {
        return when {
            accuracy >= 0.9f -> "Excellent pronunciation!"
            accuracy >= 0.7f -> "Good pronunciation, minor improvements needed"
            accuracy >= 0.5f -> "Pronunciation needs work, focus on clarity"
            accuracy >= 0.3f -> "Significant pronunciation issues detected"
            else -> "Try again, focus on each sound"
        }
    }
    
    private fun generateImprovementSuggestions(
        targetText: String,
        userText: String,
        phonemeComparisons: List<PhonemeComparison>,
        overallScore: Float
    ): List<PronunciationSuggestion> {
        val suggestions = mutableListOf<PronunciationSuggestion>()
        
        // Overall performance suggestions
        when {
            overallScore >= 0.9f -> {
                suggestions.add(
                    PronunciationSuggestion(
                        type = SuggestionType.GENERAL_TIP,
                        message = "Excellent pronunciation! Keep practicing to maintain this level."
                    )
                )
            }
            overallScore >= 0.7f -> {
                suggestions.add(
                    PronunciationSuggestion(
                        type = SuggestionType.GENERAL_TIP,
                        message = "Good pronunciation! Focus on the words that need improvement."
                    )
                )
            }
            overallScore >= 0.5f -> {
                suggestions.add(
                    PronunciationSuggestion(
                        type = SuggestionType.GENERAL_TIP,
                        message = "Practice speaking more slowly and clearly. Focus on each word."
                    )
                )
            }
            else -> {
                suggestions.add(
                    PronunciationSuggestion(
                        type = SuggestionType.GENERAL_TIP,
                        message = "Try listening to the pronunciation first, then repeat slowly."
                    )
                )
            }
        }
        
        // Specific phoneme suggestions
        val poorPhonemes = phonemeComparisons.filter { it.accuracy < 0.6f }
        if (poorPhonemes.isNotEmpty()) {
            poorPhonemes.take(3).forEach { comparison -> // Limit to top 3 issues
                suggestions.add(
                    PronunciationSuggestion(
                        type = SuggestionType.PHONEME_ACCURACY,
                        message = "Focus on pronouncing '${comparison.phoneme}' more clearly",
                        targetPhoneme = comparison.phoneme
                    )
                )
            }
        }
        
        // Rhythm and timing suggestions
        val targetWords = targetText.split("\\s+".toRegex())
        val userWords = userText.split("\\s+".toRegex())
        
        if (abs(targetWords.size - userWords.size) > 1) {
            suggestions.add(
                PronunciationSuggestion(
                    type = SuggestionType.RHYTHM_TIMING,
                    message = "Pay attention to the number of words and pauses between them"
                )
            )
        }
        
        // Stress pattern suggestions for longer phrases
        if (targetWords.size > 3 && overallScore < 0.7f) {
            suggestions.add(
                PronunciationSuggestion(
                    type = SuggestionType.STRESS_PATTERN,
                    message = "Practice the rhythm and stress pattern of the entire phrase"
                )
            )
        }
        
        return suggestions
    }
    
    suspend fun generateReferencePronunciation(text: String, language: String): String? {
        // This would integrate with TTS to generate reference pronunciation
        // For now, return null as TTS integration is handled elsewhere
        return null
    }
    
    fun calculatePronunciationImprovement(
        previousScore: Float?,
        currentScore: Float
    ): Float {
        return if (previousScore != null) {
            currentScore - previousScore
        } else {
            0f
        }
    }
    
    fun generateDetailedFeedback(analysis: PronunciationAnalysis): String {
        val feedback = StringBuilder()
        
        feedback.append("Overall Score: ${(analysis.overallScore * 100).toInt()}%\n\n")
        
        if (analysis.phonemeScores.isNotEmpty()) {
            feedback.append("Word-by-word analysis:\n")
            analysis.phonemeScores.forEach { comparison ->
                val percentage = (comparison.accuracy * 100).toInt()
                feedback.append("• ${comparison.phoneme}: ${percentage}% - ${comparison.feedback}\n")
            }
            feedback.append("\n")
        }
        
        if (analysis.suggestions.isNotEmpty()) {
            feedback.append("Suggestions for improvement:\n")
            analysis.suggestions.forEach { suggestion ->
                feedback.append("• ${suggestion.message}\n")
            }
        }
        
        return feedback.toString()
    }
}
