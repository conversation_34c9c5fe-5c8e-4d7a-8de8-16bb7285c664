package com.tfkcolin.practice.features.share.data

import android.net.Uri

sealed class ShareContentType {
    data class Text(val text: String, val title: String?) : ShareContentType()
    data class SingleFile(val uri: Uri, val mimeType: String) : ShareContentType()
    data class MultipleFiles(val uris: List<Uri>, val mimeType: String) : ShareContentType()
    data object Unsupported : ShareContentType()
}

enum class ProcessingStep {
    IDLE,
    DETECTING_TYPE,
    EXTRACTING_TEXT,
    TRANSCRIBING_AUDIO,
    PROCESSING_TEXT,
    SAVING_RESULTS,
    COMPLETED,
    ERROR
}

data class ShareProcessingResult(
    val success: <PERSON>olean,
    val translationId: String? = null,
    val error: String? = null
)