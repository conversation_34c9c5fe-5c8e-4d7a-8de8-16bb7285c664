package com.tfkcolin.practice.features.audio.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.audio.domain.AudioPracticeViewModel
import com.tfkcolin.practice.features.audio.data.AudioProcessingProgress
import com.tfkcolin.practice.features.audio.data.ProcessingStage
import com.tfkcolin.practice.ui.theme.*
import androidx.compose.runtime.LaunchedEffect

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AudioPracticeScreen(
    selectedFilePath: String = "",
    modifier: Modifier = Modifier,
    onClose: () -> Unit = {},
    onNavigateToTranslationResults: (String) -> Unit = {},
    onNavigateToValidation: (String) -> Unit = {},
    viewModel: AudioPracticeViewModel = hiltViewModel()
) {
    // Auto-select the file when screen opens
    LaunchedEffect(selectedFilePath) {
        if (selectedFilePath.isNotEmpty()) {
            val file = java.io.File(selectedFilePath)
            if (file.exists()) {
                viewModel.selectAudioFile(file)
            }
        }
    }

    // Navigate to translation results when processing is complete
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(uiState.isProcessingComplete) {
        if (uiState.isProcessingComplete) {
            val translationId = viewModel.getCurrentTranslationId()
            if (translationId != null) {
                onNavigateToTranslationResults(translationId)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Audio Transcription",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onClose) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = PrimaryBlack
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Extract Text from Audio",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Select an audio file to transcribe and analyze its content for translation and vocabulary learning",
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            // File selection and processing section
            AudioProcessingSection(uiState, viewModel, onNavigateToValidation)
        }
    }
}

@Composable
private fun AudioProcessingSection(
    uiState: com.tfkcolin.practice.features.audio.domain.AudioPracticeUiState,
    viewModel: AudioPracticeViewModel,
    onNavigateToValidation: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = LightGray),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // File info or selection prompt
            if (uiState.selectedFile != null) {
                FileInfoDisplay(uiState)
            } else {
                FileSelectionPrompt()
            }

            // Transcribe button
            if (uiState.selectedFile != null && !uiState.isTranscribing && !uiState.isProcessingText && uiState.transcription.isEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = { viewModel.transcribeSelectedFile() },
                    enabled = uiState.isFileValid,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = AccentBlue,
                        disabledContainerColor = LightGray
                    )
                ) {
                    Text("Transcribe & Analyze Audio")
                }
            }

            // Detailed progress display
            uiState.processingProgress?.let { progress ->
                Spacer(modifier = Modifier.height(16.dp))
                ProcessingProgressDisplay(progress)
            }

            // Legacy transcription progress (fallback)
            if (uiState.isTranscribing && uiState.processingProgress == null) {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator(color = AccentBlue)
                Spacer(modifier = Modifier.height(8.dp))
                Text("Transcribing audio...", color = DarkGray)
            }

            // Legacy AI Processing progress (fallback)
            if (uiState.isProcessingText && !uiState.isTranscribing && uiState.processingProgress == null) {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator(color = AccentGreen)
                Spacer(modifier = Modifier.height(8.dp))
                Text("Analyzing text with AI...", color = DarkGray)
            }

            // Transcription result
            if (uiState.transcription.isNotEmpty() && !uiState.isProcessingText) {
                Spacer(modifier = Modifier.height(16.dp))
                TranscriptionDisplay(uiState.transcription)

                // Proceed button
                if (uiState.showProceedButton) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = { onNavigateToValidation(uiState.transcription) },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(containerColor = AccentBlue)
                    ) {
                        Text("Continue to Validation")
                    }
                }
            }

            // Processing complete message
            if (uiState.isProcessingComplete) {
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    colors = CardDefaults.cardColors(containerColor = AccentGreen.copy(alpha = 0.1f))
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = AccentGreen
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Audio processed successfully! Navigating to results...",
                            color = AccentGreen,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // Error displays
            uiState.fileError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(error, color = ErrorRed, textAlign = TextAlign.Center)
            }

            uiState.transcriptionError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(error, color = ErrorRed, textAlign = TextAlign.Center)
            }

            uiState.processingError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(error, color = ErrorRed, textAlign = TextAlign.Center)
            }
        }
    }
}

@Composable
private fun FileInfoDisplay(uiState: com.tfkcolin.practice.features.audio.domain.AudioPracticeUiState) {
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = LightGray,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Selected File:",
                fontWeight = FontWeight.Bold,
                color = DarkGray
            )
            Text(
                text = uiState.selectedFile?.name ?: "",
                color = PrimaryBlack
            )
            Text(
                text = "Size: ${(uiState.selectedFile?.length()?.div(1024) ?: 0)} KB",
                fontSize = 12.sp,
                color = MediumGray
            )
        }
    }
}

@Composable
private fun FileSelectionPrompt() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "No audio file was provided.",
            textAlign = TextAlign.Center,
            color = DarkGray,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "Please use the 'Select Audio File' screen to choose a file for practice.",
            textAlign = TextAlign.Center,
            color = MediumGray,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
private fun TranscriptionDisplay(transcription: String) {
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = LightGray,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Transcription:",
                fontWeight = FontWeight.Bold,
                color = DarkGray
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = transcription,
                color = PrimaryBlack,
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
private fun ProcessingProgressDisplay(progress: AudioProcessingProgress) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (progress.stage) {
                ProcessingStage.ERROR -> ErrorRed.copy(alpha = 0.1f)
                ProcessingStage.COMPLETED -> AccentGreen.copy(alpha = 0.1f)
                else -> AccentBlue.copy(alpha = 0.1f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Progress indicator
            when (progress.stage) {
                ProcessingStage.ERROR -> {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = "Error",
                        tint = ErrorRed,
                        modifier = Modifier.size(48.dp)
                    )
                }
                ProcessingStage.COMPLETED -> {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "Completed",
                        tint = AccentGreen,
                        modifier = Modifier.size(48.dp)
                    )
                }
                else -> {
                    CircularProgressIndicator(
                        progress = { (progress.current.toFloat() / progress.total.toFloat()) },
                        color = when (progress.stage) {
                            ProcessingStage.ANALYZING_AUDIO -> AccentBlue
                            ProcessingStage.SPLITTING_AUDIO -> AccentPurple
                            ProcessingStage.TRANSCRIBING_SEGMENTS -> AccentOrange
                            ProcessingStage.PROCESSING_TEXT -> AccentGreen
                            else -> AccentBlue
                        },
                        modifier = Modifier.size(48.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Stage title
            Text(
                text = getStageDisplayName(progress.stage),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = when (progress.stage) {
                    ProcessingStage.ERROR -> ErrorRed
                    ProcessingStage.COMPLETED -> AccentGreen
                    else -> PrimaryBlack
                }
            )

            Spacer(modifier = Modifier.height(4.dp))

            // Progress details
            Text(
                text = progress.message,
                style = MaterialTheme.typography.bodyMedium,
                color = DarkGray,
                textAlign = TextAlign.Center
            )

            // Progress bar for multi-step stages
            if (progress.total > 1 && progress.stage != ProcessingStage.ERROR && progress.stage != ProcessingStage.COMPLETED) {
                Spacer(modifier = Modifier.height(8.dp))
                LinearProgressIndicator(
                    progress = { (progress.current.toFloat() / progress.total.toFloat()) },
                    modifier = Modifier.fillMaxWidth(),
                    color = when (progress.stage) {
                        ProcessingStage.ANALYZING_AUDIO -> AccentBlue
                        ProcessingStage.SPLITTING_AUDIO -> AccentPurple
                        ProcessingStage.TRANSCRIBING_SEGMENTS -> AccentOrange
                        ProcessingStage.PROCESSING_TEXT -> AccentGreen
                        else -> AccentBlue
                    }
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "${progress.current} / ${progress.total}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MediumGray
                )
            }
        }
    }
}

private fun getStageDisplayName(stage: ProcessingStage): String {
    return when (stage) {
        ProcessingStage.ANALYZING_AUDIO -> "Analyzing Audio"
        ProcessingStage.SPLITTING_AUDIO -> "Splitting Audio"
        ProcessingStage.TRANSCRIBING_SEGMENTS -> "Transcribing Segments"
        ProcessingStage.PROCESSING_TEXT -> "Processing Text"
        ProcessingStage.COMPLETED -> "Processing Complete"
        ProcessingStage.ERROR -> "Processing Error"
    }
}