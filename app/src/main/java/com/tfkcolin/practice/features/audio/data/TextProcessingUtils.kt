package com.tfkcolin.practice.features.audio.data

object TextProcessingUtils {

    /**
     * Extract words from text, cleaning punctuation and normalizing whitespace
     */
    fun extractWords(text: String): List<String> {
        return text
            .replace(Regex("[.,!?;:\"']"), "") // Remove punctuation
            .split(Regex("\\s+")) // Split by whitespace
            .map { it.trim() }
            .filter { it.isNotEmpty() }
            .map { it.lowercase() } // Normalize to lowercase for comparison
    }

    /**
     * Split text into phrases based on sentence-ending punctuation
     */
    fun extractPhrases(text: String): List<String> {
        // Split by sentence-ending punctuation but keep the punctuation
        val phrases = mutableListOf<String>()
        var currentPhrase = StringBuilder()

        text.forEach { char ->
            currentPhrase.append(char)
            if (char in setOf('.', '!', '?', ';', ':')) {
                phrases.add(currentPhrase.toString().trim())
                currentPhrase = StringBuilder()
            }
        }

        // Add any remaining text as the last phrase
        if (currentPhrase.isNotEmpty()) {
            phrases.add(currentPhrase.toString().trim())
        }

        return phrases.filter { it.isNotEmpty() }
    }

    /**
     * Normalize text for comparison (remove extra whitespace, normalize case)
     */
    fun normalizeText(text: String): String {
        return text
            .trim()
            .replace(Regex("\\s+"), " ") // Replace multiple whitespace with single space
            .lowercase()
    }

    /**
     * Check if user's spoken text matches the expected text (with fuzzy matching)
     */
    fun isTextMatch(userText: String, expectedText: String, tolerance: Double = 0.8): Boolean {
        val normalizedUser = normalizeText(userText)
        val normalizedExpected = normalizeText(expectedText)

        // Exact match
        if (normalizedUser == normalizedExpected) {
            return true
        }

        // Simple fuzzy match based on word overlap
        val userWords = extractWords(normalizedUser)
        val expectedWords = extractWords(normalizedExpected)

        if (userWords.isEmpty() || expectedWords.isEmpty()) {
            return false
        }

        val matchingWords = userWords.intersect(expectedWords.toSet())
        val matchRatio = matchingWords.size.toDouble() / expectedWords.size.toDouble()

        return matchRatio >= tolerance
    }

    /**
     * Calculate progress percentage
     */
    fun calculateProgress(current: Int, total: Int): Int {
        if (total == 0) return 0
        return ((current.toDouble() / total.toDouble()) * 100).toInt()
    }

    /**
     * Calculate overall progress with weights
     */
    fun calculateOverallProgress(
        wordProgress: Int,
        phraseProgress: Int,
        fullTextProgress: Int,
        wordWeight: Double = 0.4,
        phraseWeight: Double = 0.4,
        fullTextWeight: Double = 0.2
    ): Int {
        return ((wordProgress * wordWeight) +
                (phraseProgress * phraseWeight) +
                (fullTextProgress * fullTextWeight)).toInt()
    }
}