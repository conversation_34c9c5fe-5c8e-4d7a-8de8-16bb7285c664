package com.tfkcolin.practice.features.audio.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.home.domain.HomeViewModel
import com.tfkcolin.practice.ui.theme.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AudioFileSelectionScreen(
    modifier: Modifier = Modifier,
    onFileSelected: (File) -> Unit,
    onBackPressed: () -> Unit,
    onRequestAudioFileImport: () -> Unit = {},
    homeViewModel: HomeViewModel = hiltViewModel()
) {
    val homeUiState by homeViewModel.uiState.collectAsStateWithLifecycle()
    val audioFiles = remember { homeViewModel.getRecordingFiles() }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        "Select Audio File",
                        color = PrimaryBlack,
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = PrimaryBlack
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing,
        containerColor = PrimaryWhite
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Import button section
            Card(
                modifier = Modifier
                    .fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AccentBlue.copy(alpha = 0.1f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onRequestAudioFileImport() }
                        .padding(20.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(AccentBlue, RoundedCornerShape(12.dp)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Import",
                            tint = PrimaryWhite,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Import New Audio File",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = AccentBlue
                        )
                        Text(
                            text = "Add audio files from your device",
                            style = MaterialTheme.typography.bodyMedium,
                            color = DarkGray
                        )
                    }
                }
            }

            // Audio files list
            if (audioFiles.isEmpty()) {
                // Empty state
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.AudioFile,
                            contentDescription = "No files",
                            modifier = Modifier.size(64.dp),
                            tint = MediumGray
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Text(
                            text = "No Audio Files Found",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = DarkGray
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "Record conversations or import audio files from the home screen to start practicing",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MediumGray,
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(24.dp))
                        
                        Button(
                            onClick = onRequestAudioFileImport,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = AccentBlue
                            )
                        ) {
                            Icon(
                                Icons.Default.FolderOpen,
                                contentDescription = "Import",
                                modifier = Modifier.size(18.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Import Audio File")
                        }
                    }
                }
            } else {
                // Files list
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    item {
                        Text(
                            text = "Tap an audio file to start practicing:",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = DarkGray,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                    }
                    
                    items(audioFiles) { audioFile ->
                        AudioFileItem(
                            audioFile = audioFile,
                            onFileClick = { onFileSelected(audioFile) }
                        )
                    }
                }
            }

            // Success message display
            homeUiState.successMessage?.let { message ->
                LaunchedEffect(message) {
                    kotlinx.coroutines.delay(3000)
                    homeViewModel.clearSuccess()
                }
            }
        }
    }
}

@Composable
private fun AudioFileItem(
    audioFile: File,
    onFileClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val dateFormat = remember { SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault()) }
    val fileSizeKB = (audioFile.length() / 1024).toInt()
    val lastModified = dateFormat.format(Date(audioFile.lastModified()))

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onFileClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = LightGray
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // File icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = when (audioFile.extension.lowercase()) {
                            "mp3" -> SuccessGreen
                            "m4a", "aac" -> AccentBlue
                            "wav" -> ErrorRed.copy(alpha = 0.7f)
                            else -> MediumGray
                        },
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.AudioFile,
                    contentDescription = "Audio file",
                    tint = PrimaryWhite,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // File details
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = audioFile.nameWithoutExtension,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = PrimaryBlack,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${audioFile.extension.uppercase()} • ${fileSizeKB} KB",
                        style = MaterialTheme.typography.bodySmall,
                        color = MediumGray
                    )
                }
                
                Text(
                    text = lastModified,
                    style = MaterialTheme.typography.bodySmall,
                    color = MediumGray
                )
            }
            
            // Play/Select icon
            Icon(
                Icons.Default.PlayArrow,
                contentDescription = "Select file",
                tint = AccentBlue,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}