package com.tfkcolin.practice.features.text.data

import com.tfkcolin.practice.data.model.Translation
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import javax.inject.Inject
import javax.inject.Singleton

enum class ProcessingStep {
    IDLE,
    DETECTING_LANGUAGE,
    PROCESSING_AI,
    SAVING_RESULTS,
    COMPLETED,
    ERROR
}

interface TextProcessingPipeline {
    fun processText(
        text: String,
        source: String,
        targetLanguage: String
    ): Flow<ProcessingResult>
}

fun TextProcessingPipeline.processText(
    text: String,
    source: String
): Flow<ProcessingResult> = processText(text, source, "en")

sealed class ProcessingResult {
    data class Progress(val step: ProcessingStep) : ProcessingResult()
    data class Success(val translation: Translation) : ProcessingResult()
    data class Error(val exception: Exception) : ProcessingResult()
}

class UnifiedTextProcessingPipeline @Inject constructor(
    private val languageDetectionService: LanguageDetectionService,
    private val aiTextProcessingService: AITextProcessingService,
    private val translationRepository: TranslationRepository
) : TextProcessingPipeline {

    override fun processText(
        text: String,
        source: String,
        targetLanguage: String
    ): Flow<ProcessingResult> = flow {
        try {
            // Step 1: Language Detection
            emit(ProcessingResult.Progress(ProcessingStep.DETECTING_LANGUAGE))
            val languageResult = languageDetectionService.detectLanguage(text)
            val sourceLanguage = languageResult.getOrElse { "auto" }

            // Step 2: AI Processing
            emit(ProcessingResult.Progress(ProcessingStep.PROCESSING_AI))
            val translation = suspendCancellableCoroutine { continuation ->
                aiTextProcessingService.processTextForTranslation(
                    text = text,
                    sourceLanguage = sourceLanguage,
                    targetLanguage = targetLanguage
                ) { result ->
                    result.fold(
                        onSuccess = { translation ->
                            continuation.resume(translation)
                        },
                        onFailure = { exception ->
                            continuation.resumeWithException(exception)
                        }
                    )
                }
            }

            // Step 3: Save Results
            emit(ProcessingResult.Progress(ProcessingStep.SAVING_RESULTS))
            translationRepository.saveTranslation(translation)

            // Step 4: Complete
            emit(ProcessingResult.Progress(ProcessingStep.COMPLETED))
            emit(ProcessingResult.Success(translation))

        } catch (e: Exception) {
            emit(ProcessingResult.Progress(ProcessingStep.ERROR))
            emit(ProcessingResult.Error(e))
        }
    }
}