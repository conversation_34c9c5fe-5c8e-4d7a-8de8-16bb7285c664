package com.tfkcolin.practice.features.text.data

/**
 * Utility class for validating and normalizing language codes used in translation
 */
object LanguageValidator {

    // Supported target languages for translation
    private val supportedTargetLanguages = setOf(
        "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar", "hi",
        "nl", "sv", "da", "no", "fi", "pl", "cs", "sk", "hu", "tr", "th", "vi"
    )

    // Language pairs that are well supported by AI translation services
    private val supportedLanguagePairs = mapOf(
        "auto" to supportedTargetLanguages,
        "en" to supportedTargetLanguages,
        "es" to setOf("en", "fr", "de", "pt", "it"),
        "fr" to setOf("en", "es", "de", "it", "pt"),
        "de" to setOf("en", "fr", "es", "it", "pt"),
        "it" to setOf("en", "fr", "de", "es", "pt"),
        "pt" to setOf("en", "es", "fr", "de", "it"),
        "ru" to setOf("en"),
        "ja" to setOf("en"),
        "ko" to setOf("en"),
        "zh" to setOf("en"),
        "ar" to setOf("en"),
        "hi" to setOf("en")
    )

    /**
     * Validates if a language code is supported for translation
     */
    fun isValidLanguageCode(code: String?): Boolean {
        return code != null && (code == "auto" || supportedTargetLanguages.contains(code))
    }

    /**
     * Normalizes a language code, returning "auto" for invalid codes
     */
    fun normalizeLanguageCode(code: String?): String {
        return when {
            code == null || code == "und" -> "auto"
            supportedTargetLanguages.contains(code) -> code
            else -> "auto" // Fallback for unsupported languages
        }
    }

    /**
     * Validates if a source-target language pair is supported
     */
    fun isSupportedPair(source: String, target: String): Boolean {
        return supportedLanguagePairs[source]?.contains(target) ?: false
    }

    /**
     * Gets the best fallback target language for a given source
     */
    fun getFallbackTarget(source: String): String {
        return when (source) {
            "en" -> "es"
            "es", "fr", "de", "it", "pt" -> "en"
            else -> "en" // Default fallback
        }
    }

    /**
     * Gets all supported target languages
     */
    fun getSupportedTargetLanguages(): Set<String> {
        return supportedTargetLanguages
    }

    /**
     * Gets supported target languages for a specific source language
     */
    fun getSupportedTargetsForSource(source: String): Set<String> {
        return supportedLanguagePairs[source] ?: setOf("en")
    }

    /**
     * Gets the display name for a language code
     */
    fun getDisplayName(languageCode: String): String {
        return when (languageCode) {
            "en" -> "English"
            "es" -> "Spanish"
            "fr" -> "French"
            "de" -> "German"
            "it" -> "Italian"
            "pt" -> "Portuguese"
            "ru" -> "Russian"
            "ja" -> "Japanese"
            "ko" -> "Korean"
            "zh" -> "Chinese"
            "ar" -> "Arabic"
            "hi" -> "Hindi"
            "nl" -> "Dutch"
            "sv" -> "Swedish"
            "da" -> "Danish"
            "no" -> "Norwegian"
            "fi" -> "Finnish"
            "pl" -> "Polish"
            "cs" -> "Czech"
            "sk" -> "Slovak"
            "hu" -> "Hungarian"
            "tr" -> "Turkish"
            "th" -> "Thai"
            "vi" -> "Vietnamese"
            else -> languageCode.uppercase()
        }
    }
}