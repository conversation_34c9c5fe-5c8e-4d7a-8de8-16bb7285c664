package com.tfkcolin.practice.features.home.domain

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.MediaRecorder
import android.net.Uri
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class HomeUiState(
    val isRecording: Boolean = false,
    val recordingDuration: Long = 0L,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val lastRecordingPath: String? = null,
    val hasRecordPermission: Boolean = false,
    val permissionRequested: Boolean = false
)

@HiltViewModel
class HomeViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val preferencesRepository: PreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private var mediaRecorder: MediaRecorder? = null
    private var recordingStartTime: Long = 0L
    private var recordingFile: File? = null

    init {
        checkPermissions()
    }

    private fun checkPermissions() {
        val hasPermission = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED

        _uiState.value = _uiState.value.copy(hasRecordPermission = hasPermission)
    }

    fun onPermissionResult(granted: Boolean) {
        _uiState.value = _uiState.value.copy(
            hasRecordPermission = granted,
            permissionRequested = true,
            errorMessage = if (!granted) "Microphone permission is required to record conversations" else null
        )
    }

    /**
     * Gets the recordings directory for the currently selected language
     */
    private fun getLanguageRecordingsDir(): File {
        val baseRecordingsDir = File(context.filesDir, "recordings")
        val preferences = preferencesRepository.getCurrentPreferences()
        val languageCode = preferences.selectedLanguageCode ?: "unknown"

        val languageDir = File(baseRecordingsDir, languageCode)
        if (!languageDir.exists()) {
            languageDir.mkdirs()
        }
        return languageDir
    }

    fun requestPermission() {
        _uiState.value = _uiState.value.copy(permissionRequested = true)
    }

    fun startRecording() {
        viewModelScope.launch {
            // Check permission before attempting to record
            if (!_uiState.value.hasRecordPermission) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Microphone permission is required to record conversations"
                )
                return@launch
            }

            try {
                val recordingsDir = getLanguageRecordingsDir()

                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                recordingFile = File(recordingsDir, "conversation_$timestamp.m4a")

                mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    MediaRecorder(context)
                } else {
                    @Suppress("DEPRECATION")
                    MediaRecorder()
                }.apply {
                    setAudioSource(MediaRecorder.AudioSource.MIC)
                    setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                    setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                    setOutputFile(recordingFile?.absolutePath)
                    
                    prepare()
                    start()
                }

                recordingStartTime = System.currentTimeMillis()
                _uiState.value = _uiState.value.copy(
                    isRecording = true,
                    errorMessage = null
                )

                // Start duration tracking
                startDurationTracking()

            } catch (e: IOException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to start recording: ${e.message}"
                )
            } catch (e: SecurityException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Microphone permission required"
                )
            }
        }
    }

    fun stopRecording() {
        viewModelScope.launch {
            try {
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                mediaRecorder = null

                val fileName = recordingFile?.name ?: "recording"
                _uiState.value = _uiState.value.copy(
                    isRecording = false,
                    recordingDuration = 0L,
                    lastRecordingPath = recordingFile?.absolutePath,
                    successMessage = "Recording saved successfully: $fileName"
                )

            } catch (e: RuntimeException) {
                _uiState.value = _uiState.value.copy(
                    isRecording = false,
                    recordingDuration = 0L,
                    errorMessage = "Failed to stop recording: ${e.message}"
                )
            }
        }
    }

    private fun startDurationTracking() {
        viewModelScope.launch {
            while (_uiState.value.isRecording) {
                val duration = System.currentTimeMillis() - recordingStartTime
                _uiState.value = _uiState.value.copy(recordingDuration = duration)
                kotlinx.coroutines.delay(100) // Update every 100ms
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(successMessage = null)
    }

    fun getRecordingFiles(): List<File> {
        val recordingsDir = File(context.filesDir, "recordings")
        return if (recordingsDir.exists()) {
            recordingsDir.walk()
                .filter { it.isFile && it.extension in listOf("m4a", "mp3", "wav", "aac") }
                .sortedByDescending { it.lastModified() }
                .toList()
        } else {
            emptyList()
        }
    }

    fun importAudioFile(uri: Uri) {
        viewModelScope.launch {
            try {
                val recordingsDir = getLanguageRecordingsDir()

                // Get original file name and extension
                val originalName = getFileNameFromUri(uri)
                val extension = originalName?.substringAfterLast(".", "m4a") ?: "m4a"

                // Create a unique filename to avoid conflicts
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val importedFile = File(recordingsDir, "imported_${timestamp}_${originalName ?: "audio"}")

                // Copy the file
                context.contentResolver.openInputStream(uri)?.use { inputStream ->
                    FileOutputStream(importedFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }

                val fileName = importedFile.name
                _uiState.value = _uiState.value.copy(
                    errorMessage = null,
                    successMessage = "Audio file imported successfully: $fileName"
                )

            } catch (e: IOException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to import audio file: ${e.message}"
                )
            } catch (e: SecurityException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Permission denied: Unable to access the selected file"
                )
            }
        }
    }

    private fun getFileNameFromUri(uri: Uri): String? {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex("_display_name")
                    if (nameIndex != -1) {
                        cursor.getString(nameIndex)
                    } else {
                        null
                    }
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    override fun onCleared() {
        super.onCleared()
        mediaRecorder?.release()
        mediaRecorder = null
    }
}
