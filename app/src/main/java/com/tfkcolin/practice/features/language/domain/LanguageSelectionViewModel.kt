package com.tfkcolin.practice.features.language.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LanguageSelectionViewModel @Inject constructor(
    private val languageManager: LanguageManager
) : ViewModel() {

    val state = languageManager.state

    init {
        initializeLanguages()
    }

    private fun initializeLanguages() {
        viewModelScope.launch {
            languageManager.initializeLanguages()
        }
    }

    fun selectLanguage(language: SupportedLanguage) {
        viewModelScope.launch {
            languageManager.selectLanguage(language)
        }
    }

    fun retryInitialization() {
        initializeLanguages()
    }
}
