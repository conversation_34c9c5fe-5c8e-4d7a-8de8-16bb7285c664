package com.tfkcolin.practice.features.speech.domain

import java.util.Locale

data class SpeechUiState(
    val recognizedText: String = "",
    val selectedEngine: String = "android",
    val isInitializing: Boolean = false,
    val isEngineReady: Boolean = false,
    val isListening: Boolean = false,
    val statusMessage: String = "",
    val availableLanguages: List<Locale> = emptyList(),
    val selectedLanguage: Locale = Locale.getDefault(),
    val searchQuery: String = "",
    val isSearching: Boolean = false,
    val hasRecordAudioPermission: Boolean = false
)
