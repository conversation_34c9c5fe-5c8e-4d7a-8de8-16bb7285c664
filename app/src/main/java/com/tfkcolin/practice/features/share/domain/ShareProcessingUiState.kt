package com.tfkcolin.practice.features.share.domain

import com.tfkcolin.practice.features.share.data.ProcessingStep

data class ShareProcessingUiState(
    val processingStep: ProcessingStep = ProcessingStep.IDLE,
    val contentType: String = "",
    val contentDescription: String = "",
    val isLoading: Boolean = false,
    val progress: Float = 0f,
    val error: String? = null,
    val successMessage: String? = null
)