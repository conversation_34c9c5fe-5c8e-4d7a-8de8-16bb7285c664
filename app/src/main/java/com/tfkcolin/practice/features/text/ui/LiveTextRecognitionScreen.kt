package com.tfkcolin.practice.features.text.ui

import android.graphics.Bitmap
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.automirrored.filled.ArrowBack

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LiveTextRecognitionScreen(
    onNavigateToCapture: (String) -> Unit,
    onBackPressed: () -> Unit
) {
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val configuration = LocalConfiguration.current

    var textBlocks by remember { mutableStateOf<List<RecognizedTextBlock>>(emptyList()) }
    var selectedTextBlock by remember { mutableStateOf<RecognizedTextBlock?>(null) }
    val captureTrigger = remember { mutableStateOf(false) }

    val textRecognitionProcessor = remember { TextRecognitionProcessor() }

    // Reset text blocks when orientation changes
    LaunchedEffect(configuration.orientation) {
        textBlocks = emptyList()
        selectedTextBlock = null
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Live Text Recognition",
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = androidx.compose.material.icons.Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize().padding(paddingValues)) {
            // Camera preview
            LiveCameraPreview(
                modifier = Modifier.fillMaxSize(),
                onImageCapture = { imageProxy ->
                    textRecognitionProcessor.processImage(
                        imageProxy = imageProxy,
                        onSuccess = { visionTextBlocks ->
                            // Update the text blocks
                            textBlocks = visionTextBlocks
                            // If no selection, auto-select the first block if available
                            if (selectedTextBlock == null && visionTextBlocks.isNotEmpty()) {
                                selectedTextBlock = visionTextBlocks.first().copy(isSelected = true)
                            }
                        },
                        onFailure = { e ->
                            // Handle error - could show a snackbar or toast
                            e.printStackTrace()
                        }
                    )
                },
                captureTrigger = captureTrigger,
                onBitmapCaptured = { bitmap ->
                    selectedTextBlock?.let { block ->
                        val rect = block.boundingBox
                        // Crop bitmap to the bounding box
                        val croppedBitmap = Bitmap.createBitmap(
                            bitmap,
                            rect.left.coerceAtLeast(0),
                            rect.top.coerceAtLeast(0),
                            (rect.width()).coerceAtMost(bitmap.width - rect.left),
                            (rect.height()).coerceAtMost(bitmap.height - rect.top)
                        )

                        // Process the cropped bitmap to extract text
                        textRecognitionProcessor.processImage(
                            croppedBitmap,
                            onSuccess = { extractedText ->
                                onNavigateToCapture(extractedText)
                            },
                            onFailure = { e ->
                                e.printStackTrace()
                                // Handle error - could show snackbar
                            }
                        )
                    }
                }
            )

            // Bounding box overlay
            BoundingBoxOverlay(
                modifier = Modifier.fillMaxSize(),
                textBlocks = textBlocks,
                selectedTextBlock = selectedTextBlock,
                onTextBlockClick = { clickedBlock ->
                    selectedTextBlock = if (selectedTextBlock?.text == clickedBlock.text) {
                        null // Deselect if same
                    } else {
                        clickedBlock.copy(isSelected = true)
                    }
                }
            )

            // UI controls
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(16.dp)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Selected text preview
                selectedTextBlock?.let { block ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Selected Text:",
                                fontWeight = FontWeight.Bold
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = block.text,
                                maxLines = 3,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                }

                // Capture button
                Button(
                    onClick = {
                        captureTrigger.value = true
                    },
                    enabled = selectedTextBlock != null
                ) {
                    Text("Capture Selected Area")
                }

                // Clear selection button
                TextButton(
                    onClick = {
                        selectedTextBlock = null
                    },
                    enabled = selectedTextBlock != null
                ) {
                    Text("Clear Selection")
                }
            }
        }
    }

    // Clean up when the composable is disposed
    DisposableEffect(lifecycleOwner) {
        onDispose {
            textRecognitionProcessor.close()
        }
    }
}