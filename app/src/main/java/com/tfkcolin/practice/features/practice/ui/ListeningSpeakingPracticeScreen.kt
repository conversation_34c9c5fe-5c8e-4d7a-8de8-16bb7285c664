package com.tfkcolin.practice.features.practice.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.practice.domain.PracticeViewModel
import com.tfkcolin.practice.ui.components.*
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ListeningSpeakingPracticeScreen(
    modifier: Modifier = Modifier,
    onClose: () -> Unit = {},
    viewModel: PracticeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(DarkGray.copy(alpha = 0.9f))
    ) {
        // Top controls
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onClose) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = PrimaryWhite
                )
            }
            
            // Subway Mode toggle
            Surface(
                shape = RoundedCornerShape(20.dp),
                color = if (uiState.isSubwayMode) PrimaryWhite.copy(alpha = 0.9f) else PrimaryWhite.copy(alpha = 0.3f),
                modifier = Modifier.clickable { viewModel.toggleSubwayMode() }
            ) {
                Text(
                    text = "Subway Mode: ${if (uiState.isSubwayMode) "ON" else "OFF"}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (uiState.isSubwayMode) PrimaryBlack else PrimaryWhite,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }

        // Main content card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
                .align(Alignment.Center),
            colors = CardDefaults.cardColors(containerColor = PrimaryWhite),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Top action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    IconButton(onClick = { /* Share */ }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "Share",
                            tint = DarkGray
                        )
                    }
                    
                    // Microphone button
                    AudioControlButton(
                        icon = Icons.Default.Mic,
                        onClick = {
                            if (uiState.isListening) {
                                viewModel.stopListening()
                            } else {
                                viewModel.startListening()
                            }
                        },
                        backgroundColor = if (uiState.isListening) ErrorRed else MediumGray,
                        iconColor = if (uiState.isListening) PrimaryWhite else PrimaryBlack,
                        size = 48
                    )

                    IconButton(onClick = { /* Bookmark functionality */ }) {
                        Icon(
                            imageVector = Icons.Default.Bookmark,
                            contentDescription = "Bookmark",
                            tint = DarkGray
                        )
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Practice text
                Text(
                    text = uiState.practiceText,
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = AccentBlue,
                    textAlign = TextAlign.Center,
                    lineHeight = 40.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Translation text
                Text(
                    text = uiState.translationText,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Normal,
                    color = PrimaryBlack,
                    textAlign = TextAlign.Center
                )

                // Recognized text (if listening)
                if (uiState.recognizedText.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Surface(
                        shape = RoundedCornerShape(8.dp),
                        color = LightGray
                    ) {
                        Text(
                            text = "You said: \"${uiState.recognizedText}\"",
                            fontSize = 14.sp,
                            color = PrimaryBlack,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Restart button
                OutlinedButton(
                    onClick = { viewModel.restart() },
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = AccentBlue
                    ),
                    border = BorderStroke(1.dp, AccentBlue),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Restart",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Restart",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Progress indicator
        ProgressIndicator(
            current = uiState.currentStep,
            total = uiState.totalSteps,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 120.dp)
        )

        // Bottom audio controls
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Audio progress bar (simplified)
            Box(
                modifier = Modifier
                    .width(280.dp)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(PrimaryWhite.copy(alpha = 0.3f))
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(0.6f) // 60% progress
                        .background(PrimaryWhite)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Audio control buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AudioControlButton(
                    icon = Icons.Default.Replay,
                    onClick = { /* Previous */ },
                    backgroundColor = PrimaryWhite.copy(alpha = 0.2f),
                    iconColor = PrimaryWhite,
                    size = 48
                )

                AudioControlButton(
                    icon = if (uiState.isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                    onClick = {
                        if (uiState.isPlaying) {
                            viewModel.pauseAudio()
                        } else {
                            viewModel.playAudio()
                        }
                    },
                    backgroundColor = PrimaryWhite,
                    iconColor = PrimaryBlack,
                    size = 64
                )

                AudioControlButton(
                    icon = Icons.Default.Forward30,
                    onClick = { /* Next */ },
                    backgroundColor = PrimaryWhite.copy(alpha = 0.2f),
                    iconColor = PrimaryWhite,
                    size = 48
                )

//                // Speed control
//                Text(
//                    text = "1x",
//                    fontSize = 16.sp,
//                    fontWeight = FontWeight.Medium,
//                    color = PrimaryWhite,
//                    modifier = Modifier.padding(start = 8.dp)
//                )
            }
        }
    }
}
