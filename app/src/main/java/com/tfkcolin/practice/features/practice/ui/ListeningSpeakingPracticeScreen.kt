package com.tfkcolin.practice.features.practice.ui

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.practice.data.repository.PreferencesRepository
import javax.inject.Inject

@Composable
fun ListeningSpeakingPracticeScreen(
    modifier: Modifier = Modifier,
    onClose: () -> Unit = {},
    preferencesRepository: PreferencesRepository? = null
) {
    // Get the target language from user preferences
    val targetLanguage = preferencesRepository?.getCurrentPreferences()?.selectedLanguageCode ?: "en"

    // Use the enhanced practice screen
    EnhancedListeningSpeakingPracticeScreen(
        targetLanguage = targetLanguage,
        modifier = modifier,
        onClose = onClose
    )
}
