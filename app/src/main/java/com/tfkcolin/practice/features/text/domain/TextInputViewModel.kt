package com.tfkcolin.practice.features.text.domain

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

data class TextInputUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class TextInputViewModel @Inject constructor() : ViewModel() {

    private val _uiState = MutableStateFlow(TextInputUiState())
    val uiState: StateFlow<TextInputUiState> = _uiState.asStateFlow()

    fun onImageInputSelected() {
        // Navigation will be handled by the UI layer
        // This ViewModel manages state, not navigation
    }

    fun onDocumentInputSelected() {
        // Navigation will be handled by the UI layer
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}