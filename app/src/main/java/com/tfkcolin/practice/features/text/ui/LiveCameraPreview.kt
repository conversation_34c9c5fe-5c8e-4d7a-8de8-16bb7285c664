package com.tfkcolin.practice.features.text.ui

import android.content.res.Configuration
import android.util.Log
import android.view.Surface
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat

private const val TAG = "LiveCameraPreview"

@Composable
fun LiveCameraPreview(
    modifier: Modifier = Modifier,
    onImageCapture: (ImageProxy) -> Unit,
    captureTrigger: MutableState<Boolean>? = null,
    onBitmapCaptured: ((android.graphics.Bitmap) -> Unit)? = null
) {
    val context = LocalContext.current
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val configuration = LocalConfiguration.current

    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }

    // Get current device rotation
    val rotation = remember(configuration.orientation) {
        when (configuration.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> Surface.ROTATION_0
            Configuration.ORIENTATION_LANDSCAPE -> Surface.ROTATION_90
            else -> Surface.ROTATION_0
        }
    }

    // Create preview with target rotation
    val preview = remember(rotation) {
        Preview.Builder()
            .setTargetRotation(rotation)
            .build()
    }

    // Create image capture with target rotation
    val imageCapture = remember(rotation) {
        ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .setTargetRotation(rotation)
            .build()
    }

    // Create image analyzer with target rotation
    val imageAnalyzer = remember(rotation) {
        ImageAnalysis.Builder()
            .setTargetRotation(rotation)
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()
            .also {
                it.setAnalyzer(ContextCompat.getMainExecutor(context)) { imageProxy ->
                    onImageCapture(imageProxy)
                }
            }
    }

    // Handle capture trigger
    LaunchedEffect(captureTrigger?.value) {
        if (captureTrigger?.value == true && onBitmapCaptured != null) {
            captureTrigger.value = false // Reset trigger
            val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
                java.io.File(context.cacheDir, "temp_live_capture.jpg")
            ).build()

            imageCapture.takePicture(
                outputFileOptions,
                ContextCompat.getMainExecutor(context),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        try {
                            val bitmap = android.graphics.BitmapFactory.decodeFile(
                                output.savedUri?.path ?: (context.cacheDir.path + "/temp_live_capture.jpg")
                            )
                            onBitmapCaptured(bitmap)
                        } catch (e: Exception) {
                            Log.e(TAG, "Failed to decode captured image", e)
                        }
                    }

                    override fun onError(exception: ImageCaptureException) {
                        Log.e(TAG, "Photo capture failed: ${exception.message}", exception)
                    }
                }
            )
        }
    }

    AndroidView(
        modifier = modifier,
        factory = { ctx ->
            PreviewView(ctx).apply {
                layoutParams = android.view.ViewGroup.LayoutParams(
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT
                )
                scaleType = PreviewView.ScaleType.FILL_START
            }
        },
        update = { previewView ->
            cameraProviderFuture.addListener({
                try {
                    val cameraProvider = cameraProviderFuture.get()

                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll()

                    // Bind use cases to camera
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        CameraSelector.DEFAULT_BACK_CAMERA,
                        preview,
                        imageAnalyzer,
                        imageCapture
                    )

                    // Attach preview to preview view
                    preview.surfaceProvider = previewView.surfaceProvider
                } catch (exc: Exception) {
                    Log.e(TAG, "Use case binding failed", exc)
                }
            }, ContextCompat.getMainExecutor(context))
        }
    )

    // Return capture function if needed, but since composable, perhaps not
}