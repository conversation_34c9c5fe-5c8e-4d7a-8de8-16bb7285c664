package com.tfkcolin.practice.features.audio.data

import android.util.Log
import com.tfkcolin.practice.features.text.data.DocumentTextProcessingService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Processor for handling efficient audio transcription with splitting and text processing
 */
interface AudioTranscriptionProcessor {

    /**
     * Process audio file with intelligent splitting and transcription
     * @param audioFile The audio file to process
     * @param progressCallback Callback for overall progress updates
     * @param callback Callback with final processed text result or error
     */
    fun processAudioFile(
        audioFile: File,
        progressCallback: ((AudioProcessingProgress) -> Unit)? = null,
        callback: (Result<String>) -> Unit
    )

    /**
     * Process transcribed text with intelligent chunking for further processing
     * @param transcribedText The transcribed text to process
     * @param callback Callback with processed text result or error
     */
    fun processTranscribedText(
        transcribedText: String,
        callback: (Result<String>) -> Unit
    )
}

data class AudioProcessingProgress(
    val stage: ProcessingStage,
    val current: Int,
    val total: Int,
    val message: String
)

enum class ProcessingStage {
    ANALYZING_AUDIO,
    SPLITTING_AUDIO,
    TRANSCRIBING_SEGMENTS,
    PROCESSING_TEXT,
    COMPLETED,
    ERROR
}

@Singleton
class EfficientAudioTranscriptionProcessor @Inject constructor(
    private val metadataService: AudioMetadataService,
    private val splitterService: AudioFileSplitterService,
    private val transcriptionService: AudioTranscriptionService,
    private val textProcessingService: DocumentTextProcessingService
) : AudioTranscriptionProcessor {

    companion object {
        private const val TAG = "AudioTranscriptionProcessor"
        private const val MAX_TRANSCRIPTION_LENGTH = 10000 // Characters
    }

    override fun processAudioFile(
        audioFile: File,
        progressCallback: ((AudioProcessingProgress) -> Unit)?,
        callback: (Result<String>) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                progressCallback?.invoke(AudioProcessingProgress(
                    ProcessingStage.ANALYZING_AUDIO, 0, 1, "Analyzing audio file..."
                ))

                // Step 1: Analyze audio file
                val metadataResult = metadataService.extractMetadata(audioFile)
                if (metadataResult.isFailure) {
                    callback(Result.failure(metadataResult.exceptionOrNull()!!))
                    return@launch
                }

                val metadata = metadataResult.getOrNull()!!
                progressCallback?.invoke(AudioProcessingProgress(
                    ProcessingStage.ANALYZING_AUDIO, 1, 1, "Audio analysis complete"
                ))

                // Step 2: Check if splitting is needed
                val needsSplitting = metadataService.isTooLong(metadata)
                val segmentFiles: List<File>

                if (needsSplitting) {
                    progressCallback?.invoke(AudioProcessingProgress(
                        ProcessingStage.SPLITTING_AUDIO, 0, 1, "Splitting long audio file..."
                    ))

                    // Split the audio file
                    val splitResult = splitterService.splitAudioFile(audioFile)
                    if (splitResult.isFailure) {
                        callback(Result.failure(splitResult.exceptionOrNull()!!))
                        return@launch
                    }

                    segmentFiles = splitResult.getOrNull()!!
                    progressCallback?.invoke(AudioProcessingProgress(
                        ProcessingStage.SPLITTING_AUDIO, 1, 1,
                        "Split into ${segmentFiles.size} segments"
                    ))
                } else {
                    segmentFiles = listOf(audioFile)
                }

                // Step 3: Transcribe segments
                progressCallback?.invoke(AudioProcessingProgress(
                    ProcessingStage.TRANSCRIBING_SEGMENTS, 0, segmentFiles.size,
                    "Transcribing audio segments..."
                ))

                var finalTranscription = ""

                if (segmentFiles.size == 1) {
                    // Single file transcription
                    transcriptionService.transcribeAudioFile(segmentFiles[0]) { result ->
                        result.fold(
                            onSuccess = { transcription ->
                                finalTranscription = transcription
                                handleTranscriptionComplete(
                                    transcription, segmentFiles, progressCallback, callback
                                )
                            },
                            onFailure = { error ->
                                callback(Result.failure(error))
                            }
                        )
                    }
                } else {
                    // Multi-segment transcription with progress tracking
                    transcriptionService.transcribeAudioSegments(
                        segmentFiles,
                        progressCallback = { current, total ->
                            progressCallback?.invoke(AudioProcessingProgress(
                                ProcessingStage.TRANSCRIBING_SEGMENTS, current, total,
                                "Transcribing segment $current of $total..."
                            ))
                        },
                        callback = { result ->
                            result.fold(
                                onSuccess = { transcription ->
                                    finalTranscription = transcription
                                    handleTranscriptionComplete(
                                        transcription, segmentFiles, progressCallback, callback
                                    )
                                },
                                onFailure = { error ->
                                    callback(Result.failure(error))
                                }
                            )
                        }
                    )
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error in audio processing pipeline", e)
                progressCallback?.invoke(AudioProcessingProgress(
                    ProcessingStage.ERROR, 0, 0, "Processing failed: ${e.message}"
                ))
                callback(Result.failure(e))
            }
        }
    }

    private fun handleTranscriptionComplete(
        transcription: String,
        segmentFiles: List<File>,
        progressCallback: ((AudioProcessingProgress) -> Unit)?,
        callback: (Result<String>) -> Unit
    ) {
        // Clean up temporary segment files if they were created
        if (segmentFiles.size > 1) {
            splitterService.cleanupSegments(segmentFiles)
        }

        // Check if transcribed text needs further processing
        if (transcription.length > MAX_TRANSCRIPTION_LENGTH) {
            progressCallback?.invoke(AudioProcessingProgress(
                ProcessingStage.PROCESSING_TEXT, 0, 1, "Processing long transcription..."
            ))

            processTranscribedText(transcription) { result ->
                progressCallback?.invoke(AudioProcessingProgress(
                    ProcessingStage.COMPLETED, 1, 1, "Processing complete"
                ))
                callback(result)
            }
        } else {
            progressCallback?.invoke(AudioProcessingProgress(
                ProcessingStage.COMPLETED, 1, 1, "Transcription complete"
            ))
            callback(Result.success(transcription))
        }
    }

    override fun processTranscribedText(
        transcribedText: String,
        callback: (Result<String>) -> Unit
    ) {
        if (transcribedText.isBlank()) {
            callback(Result.success(transcribedText))
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Use the existing document text processing logic to split long text
                val textChunks = textProcessingService.splitIntoParagraphs(
                    transcribedText,
                    maxLength = 4000 // Smaller chunks for AI processing
                )

                if (textChunks.size <= 1) {
                    callback(Result.success(transcribedText))
                } else {
                    // For now, just return the chunks joined back together
                    // In a more advanced implementation, this could process each chunk separately
                    val processedText = textChunks.joinToString("\n\n")
                    callback(Result.success(processedText))
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error processing transcribed text", e)
                callback(Result.failure(e))
            }
        }
    }
}