package com.tfkcolin.practice.features.speech.domain

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.speech.data.SpeechRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SpeechViewModel @Inject constructor(
    private val speechRepository: SpeechRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _uiState = MutableStateFlow(SpeechUiState())
    val uiState: StateFlow<SpeechUiState> = _uiState

    init {
        checkRecordAudioPermission()
        chooseEngine("android") // default
    }

    private fun checkRecordAudioPermission() {
        val hasPermission = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        _uiState.value = _uiState.value.copy(hasRecordAudioPermission = hasPermission)
    }

    fun onPermissionResult(granted: Boolean) {
        _uiState.value = _uiState.value.copy(hasRecordAudioPermission = granted)
        if (granted) {
            _uiState.value = _uiState.value.copy(statusMessage = "")
        } else {
            _uiState.value = _uiState.value.copy(statusMessage = "Microphone permission required for speech recognition")
        }
    }

    fun requestRecordAudioPermission(onRequestPermission: () -> Unit) {
        if (!_uiState.value.hasRecordAudioPermission) {
            onRequestPermission()
        }
    }

    fun onSearchQueryChange(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
    }

    fun onToggleSearch() {
        _uiState.value = _uiState.value.copy(isSearching = !_uiState.value.isSearching)
    }

    fun chooseEngine(engineKey: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isInitializing = true)
            speechRepository.chooseEngine(engineKey) { success ->
                val languages = if (success) speechRepository.getAvailableLanguages() else emptyList()
                val currentLocale = _uiState.value.selectedLanguage
                val selectedLocale = languages.firstOrNull() ?: currentLocale
                _uiState.value = _uiState.value.copy(
                    isInitializing = false,
                    selectedEngine = engineKey,
                    isEngineReady = success,
                    statusMessage = if (success) "Engine ready" else "Failed to initialize engine",
                    availableLanguages = languages,
                    selectedLanguage = selectedLocale
                )
                if (success) {
                    setSelectedLanguage(selectedLocale)
                }
            }
        }
    }

    fun startListening() {
        if (!_uiState.value.hasRecordAudioPermission) {
            _uiState.value = _uiState.value.copy(
                statusMessage = "Microphone permission required. Please grant permission to use speech recognition."
            )
            return
        }

        if (_uiState.value.isEngineReady && !speechRepository.isListening) {
            speechRepository.startListening(
                callback = { text ->
                    _uiState.value = _uiState.value.copy(
                        recognizedText = text,
                        isListening = speechRepository.isListening
                    )
                },
                errorCallback = { error ->
                    _uiState.value = _uiState.value.copy(
                        statusMessage = error,
                        isListening = false
                    )
                }
            )
            _uiState.value = _uiState.value.copy(isListening = true)
        }
    }

    fun stopListening() {
        speechRepository.stopListening()
        _uiState.value = _uiState.value.copy(isListening = false)
    }

    fun clearText() {
        _uiState.value = _uiState.value.copy(recognizedText = "")
    }


    fun queryAvailableLanguages() {
        val languages = speechRepository.getAvailableLanguages()
        _uiState.value = _uiState.value.copy(availableLanguages = languages)
    }

    fun setSelectedLanguage(locale: Locale) {
        speechRepository.setLanguage(locale)
        _uiState.value = _uiState.value.copy(selectedLanguage = locale)
    }

    override fun onCleared() {
        speechRepository.shutdown()
        super.onCleared()
    }
}