package com.tfkcolin.practice.features.practice.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.data.model.*
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedListeningSpeakingPracticeScreen(
    targetLanguage: String,
    modifier: Modifier = Modifier,
    onClose: () -> Unit = {},
    viewModel: EnhancedPracticeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // Initialize session when screen loads
    LaunchedEffect(targetLanguage) {
        viewModel.initializePracticeSession(targetLanguage)
    }
    
    // Handle errors
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error snackbar or dialog
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
    ) {
        // Top App Bar
        TopAppBar(
            title = { 
                Text(
                    text = "Listening & Speaking Practice",
                    color = PrimaryBlack,
                    fontWeight = FontWeight.SemiBold
                )
            },
            navigationIcon = {
                IconButton(onClick = onClose) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = PrimaryBlack
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = PrimaryWhite
            )
        )
        
        when {
            uiState.isLoading -> {
                LoadingScreen()
            }
            
            uiState.isSessionComplete && !uiState.finalAssessmentMode -> {
                FinalAssessmentScreen(
                    session = uiState.currentSession,
                    results = uiState.completedItems,
                    onStartFinalAssessment = viewModel::startFinalAssessment,
                    onClose = onClose
                )
            }
            
            uiState.finalAssessmentMode -> {
                FullTextAssessmentScreen(
                    session = uiState.currentSession,
                    onComplete = viewModel::completeSession,
                    onClose = onClose
                )
            }
            
            else -> {
                ItemPracticeScreen(
                    uiState = uiState,
                    viewModel = viewModel,
                    onClose = onClose
                )
            }
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator(
                color = PrimaryBlue,
                modifier = Modifier.size(48.dp)
            )
            Text(
                text = "Preparing your practice session...",
                style = MaterialTheme.typography.bodyLarge,
                color = PrimaryBlack.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun ItemPracticeScreen(
    uiState: PracticeFlowState,
    viewModel: EnhancedPracticeViewModel,
    onClose: () -> Unit
) {
    if (uiState.currentSession.practiceItems.isEmpty()) {
        EmptyStateScreen(onClose = onClose)
        return
    }
    
    val currentItem = uiState.currentSession.practiceItems[uiState.currentItemIndex]
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Progress indicator
        PracticeProgressIndicator(
            currentItem = uiState.currentItemIndex + 1,
            totalItems = uiState.currentSession.practiceItems.size,
            progress = uiState.sessionProgress
        )
        
        // Practice mode indicator
        PracticeModeHeader(
            mode = uiState.practiceMode,
            itemType = currentItem.itemType
        )
        
        // Main content area
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            when (uiState.practiceMode) {
                PracticeMode.LISTENING -> {
                    ListeningPracticeContent(
                        item = currentItem,
                        onPlayAudio = viewModel::playItemAudio,
                        onContinue = viewModel::moveToSpeakingMode
                    )
                }
                
                PracticeMode.SPEAKING -> {
                    SpeakingPracticeContent(
                        item = currentItem,
                        userResponse = uiState.userResponse,
                        pronunciationScore = uiState.pronunciationScore,
                        transcriptionResult = uiState.transcriptionResult,
                        showFeedback = uiState.showFeedback,
                        onStartRecording = viewModel::startRecording,
                        onStopRecording = viewModel::stopRecording,
                        onRetry = viewModel::retryPronunciation,
                        onContinue = viewModel::moveToComprehensionMode
                    )
                }
                
                PracticeMode.COMPREHENSION -> {
                    ComprehensionPracticeContent(
                        item = currentItem,
                        onAnswerSelected = viewModel::selectComprehensionAnswer,
                        onContinue = viewModel::completeItemPractice
                    )
                }
                
                else -> {
                    // Handle other modes if needed
                    Text("Mode not implemented: ${uiState.practiceMode}")
                }
            }
        }
        
        // Bottom controls
        PracticeBottomControls(
            canMarkAsLearned = uiState.practiceMode == PracticeMode.COMPREHENSION,
            onMarkAsLearned = { viewModel.markItemAsLearned(currentItem.id) },
            onSkip = viewModel::skipCurrentItem,
            onClose = onClose
        )
    }
}

@Composable
fun EmptyStateScreen(onClose: () -> Unit) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.School,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = PrimaryBlue.copy(alpha = 0.6f)
            )
            Text(
                text = "No Practice Content Available",
                style = MaterialTheme.typography.headlineSmall,
                color = PrimaryBlack,
                fontWeight = FontWeight.SemiBold
            )
            Text(
                text = "Import some content first to start practicing!",
                style = MaterialTheme.typography.bodyLarge,
                color = PrimaryBlack.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            Button(
                onClick = onClose,
                colors = ButtonDefaults.buttonColors(
                    containerColor = PrimaryBlue
                )
            ) {
                Text("Go Back")
            }
        }
    }
}

@Composable
fun PracticeProgressIndicator(
    currentItem: Int,
    totalItems: Int,
    progress: Float
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Item $currentItem of $totalItems",
                style = MaterialTheme.typography.bodyMedium,
                color = PrimaryBlack.copy(alpha = 0.7f)
            )
            Text(
                text = "${(progress * 100).toInt()}%",
                style = MaterialTheme.typography.bodyMedium,
                color = PrimaryBlue,
                fontWeight = FontWeight.SemiBold
            )
        }
        
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = PrimaryBlue,
            trackColor = PrimaryBlue.copy(alpha = 0.2f)
        )
    }
}

@Composable
fun PracticeModeHeader(
    mode: PracticeMode,
    itemType: PracticeItemType
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (mode) {
                PracticeMode.LISTENING -> Color(0xFFE3F2FD)
                PracticeMode.SPEAKING -> Color(0xFFE8F5E8)
                PracticeMode.COMPREHENSION -> Color(0xFFFFF3E0)
                else -> Color(0xFFF5F5F5)
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = when (mode) {
                    PracticeMode.LISTENING -> Icons.Default.VolumeUp
                    PracticeMode.SPEAKING -> Icons.Default.Mic
                    PracticeMode.COMPREHENSION -> Icons.Default.Quiz
                    else -> Icons.Default.School
                },
                contentDescription = null,
                tint = when (mode) {
                    PracticeMode.LISTENING -> Color(0xFF1976D2)
                    PracticeMode.SPEAKING -> Color(0xFF388E3C)
                    PracticeMode.COMPREHENSION -> Color(0xFFF57C00)
                    else -> PrimaryBlack
                }
            )
            
            Column {
                Text(
                    text = when (mode) {
                        PracticeMode.LISTENING -> "Listen & Learn"
                        PracticeMode.SPEAKING -> "Speak & Practice"
                        PracticeMode.COMPREHENSION -> "Test Understanding"
                        else -> "Practice"
                    },
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = PrimaryBlack
                )
                Text(
                    text = "Practicing ${itemType.name.lowercase()}",
                    style = MaterialTheme.typography.bodySmall,
                    color = PrimaryBlack.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun PracticeBottomControls(
    canMarkAsLearned: Boolean,
    onMarkAsLearned: () -> Unit,
    onSkip: () -> Unit,
    onClose: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Mark as learned button
            if (canMarkAsLearned) {
                OutlinedButton(
                    onClick = onMarkAsLearned,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF28A745)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Learned")
                }
            }

            // Skip button
            OutlinedButton(
                onClick = onSkip,
                modifier = Modifier.weight(1f)
            ) {
                Text("Skip")
            }

            // Close button
            OutlinedButton(
                onClick = onClose,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color(0xFFDC3545)
                )
            ) {
                Text("Exit")
            }
        }
    }
}
