package com.tfkcolin.practice.features.audio.data

import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.ai.FirebaseAI
import com.google.firebase.ai.GenerativeModel
import com.google.firebase.ai.type.content
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

interface AudioTranscriptionService {

    /**
     * Transcribe audio file to text
     * @param audioFile The audio file to transcribe
     * @param callback Callback with transcription result or error
     */
    fun transcribeAudioFile(
        audioFile: File,
        callback: (Result<String>) -> Unit
    )

    /**
     * Transcribe multiple audio segments with progress tracking
     * @param audioSegments List of audio segment files to transcribe
     * @param progressCallback Callback for progress updates (current, total)
     * @param callback Callback with final combined transcription result or error
     */
    fun transcribeAudioSegments(
        audioSegments: List<File>,
        progressCallback: ((Int, Int) -> Unit)? = null,
        callback: (Result<String>) -> Unit
    )

    /**
     * Check if the service is available and ready
     */
    fun isAvailable(): Boolean

    /**
     * Get supported audio formats (for validation)
     */
    fun getSupportedFormats(): List<String>
}

class FirebaseAudioTranscriptionService : AudioTranscriptionService {

    private val generativeModel: GenerativeModel by lazy {
        FirebaseAI.getInstance(FirebaseApp.getInstance()).generativeModel("gemini-2.5-flash")
    }

    override fun transcribeAudioFile(audioFile: File, callback: (Result<String>) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val audioBytes = audioFile.readBytes()
                val mimeType = getMimeType(audioFile.extension)

                val prompt = content {
                    inlineData(audioBytes, mimeType)
                    text("Transcribe what's said in this audio recording.")
                }

                val response = generativeModel.generateContent(prompt)
                val transcription = response.text ?: "No transcription available"

                callback(Result.success(transcription))
            } catch (e: Exception) {
                Log.e("FirebaseAudioTranscriptionService", "Error transcribing audio", e)
                callback(Result.failure(e))
            }
        }
    }

    override fun transcribeAudioSegments(
        audioSegments: List<File>,
        progressCallback: ((Int, Int) -> Unit)?,
        callback: (Result<String>) -> Unit
    ) {
        if (audioSegments.isEmpty()) {
            callback(Result.failure(IllegalArgumentException("No audio segments provided")))
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val transcriptions = mutableListOf<String>()
                var completedSegments = 0

                // Process each segment sequentially
                for ((index, segmentFile) in audioSegments.withIndex()) {
                    try {
                        val segmentTranscription = transcribeSingleSegment(segmentFile)
                        transcriptions.add(segmentTranscription)

                        completedSegments++
                        progressCallback?.invoke(completedSegments, audioSegments.size)

                        Log.d("FirebaseAudioTranscriptionService",
                            "Transcribed segment ${index + 1}/${audioSegments.size}")

                    } catch (e: Exception) {
                        Log.e("FirebaseAudioTranscriptionService",
                            "Error transcribing segment ${index + 1}", e)
                        callback(Result.failure(Exception("Failed to transcribe segment ${index + 1}: ${e.message}", e)))
                        return@launch
                    }
                }

                // Combine all transcriptions
                val combinedTranscription = transcriptions.joinToString("\n\n")
                callback(Result.success(combinedTranscription))

            } catch (e: Exception) {
                Log.e("FirebaseAudioTranscriptionService", "Error transcribing audio segments", e)
                callback(Result.failure(e))
            }
        }
    }

    private suspend fun transcribeSingleSegment(audioFile: File): String {
        try {
            val audioBytes = audioFile.readBytes()
            val mimeType = getMimeType(audioFile.extension)

            val prompt = content {
                inlineData(audioBytes, mimeType)
                text("Transcribe what's said in this audio recording. This is part of a longer recording.")
            }

            val response = generativeModel.generateContent(prompt)
            val transcription = response.text ?: "No transcription available"

            return transcription
        } catch (e: Exception) {
            throw e
        }
    }

    private fun getMimeType(extension: String): String {
        return when (extension.lowercase()) {
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "m4a" -> "audio/mp4"
            "aac" -> "audio/aac"
            else -> "audio/mpeg" // default
        }
    }

    override fun isAvailable(): Boolean {
        // Check if Firebase AI is available
        return try {
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun getSupportedFormats(): List<String> = listOf("m4a", "mp3", "wav", "aac")
}