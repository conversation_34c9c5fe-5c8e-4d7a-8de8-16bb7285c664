package com.tfkcolin.practice.features.share.domain

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.audio.data.AudioTranscriptionProcessor
import com.tfkcolin.practice.features.audio.data.AudioProcessingProgress
import com.tfkcolin.practice.features.audio.data.ProcessingStage
import com.tfkcolin.practice.features.share.data.ProcessingStep
import com.tfkcolin.practice.features.share.data.ShareContentType
import com.tfkcolin.practice.features.share.data.ShareProcessingResult
import com.tfkcolin.practice.features.text.data.DocumentTextProcessingService
import com.tfkcolin.practice.features.text.data.ImageTextExtractionService
import com.tfkcolin.practice.features.text.data.TextProcessingPipeline
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

private const val TAG = "ShareProcessingViewModel"

@HiltViewModel
class ShareProcessingViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val textProcessingPipeline: TextProcessingPipeline,
    private val imageTextExtractionService: ImageTextExtractionService,
    private val audioTranscriptionProcessor: AudioTranscriptionProcessor,
    private val documentTextProcessingService: DocumentTextProcessingService,
    private val preferencesRepository: PreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ShareProcessingUiState())
    val uiState: StateFlow<ShareProcessingUiState> = _uiState.asStateFlow()

    private val _processingResult = MutableStateFlow<ShareProcessingResult?>(null)
    val processingResult: StateFlow<ShareProcessingResult?> = _processingResult.asStateFlow()

    fun processSharedText(text: String, title: String?) {
        val userPrefs = preferencesRepository.getCurrentPreferences()
        val targetLanguage = userPrefs.selectedTargetLanguage

        Log.d(TAG, "Processing shared text: ${text.take(50)}..., title: $title")
        updateState(
            contentType = "Text",
            contentDescription = title ?: "Shared text",
            processingStep = ProcessingStep.PROCESSING_TEXT
        )

        viewModelScope.launch {
            textProcessingPipeline.processText(text, "shared_text", targetLanguage)
                .collect { result ->
                    when (result) {
                        is com.tfkcolin.practice.features.text.data.ProcessingResult.Progress -> {
                            val step = when (result.step) {
                                com.tfkcolin.practice.features.text.data.ProcessingStep.DETECTING_LANGUAGE -> ProcessingStep.PROCESSING_TEXT
                                com.tfkcolin.practice.features.text.data.ProcessingStep.PROCESSING_AI -> ProcessingStep.PROCESSING_TEXT
                                com.tfkcolin.practice.features.text.data.ProcessingStep.SAVING_RESULTS -> ProcessingStep.SAVING_RESULTS
                                com.tfkcolin.practice.features.text.data.ProcessingStep.COMPLETED -> ProcessingStep.COMPLETED
                                com.tfkcolin.practice.features.text.data.ProcessingStep.ERROR -> ProcessingStep.ERROR
                                else -> ProcessingStep.PROCESSING_TEXT
                            }
                            updateState(processingStep = step)
                        }
                        is com.tfkcolin.practice.features.text.data.ProcessingResult.Success -> {
                            updateState(
                                processingStep = ProcessingStep.COMPLETED,
                                successMessage = "✓ Processing completed! Translation ready."
                            )
                            _processingResult.value = ShareProcessingResult(
                                success = true,
                                translationId = result.translation.id
                            )
                        }
                        is com.tfkcolin.practice.features.text.data.ProcessingResult.Error -> {
                            updateState(
                                processingStep = ProcessingStep.ERROR,
                                error = "Failed to process text: ${result.exception.message}"
                            )
                            _processingResult.value = ShareProcessingResult(
                                success = false,
                                error = result.exception.message
                            )
                        }
                    }
                }
        }
    }

    fun processSharedFile(uri: Uri, mimeType: String) {
        Log.d(TAG, "Processing shared file: $uri, MIME: $mimeType")
        val contentType = determineContentType(mimeType)
        updateState(
            contentType = contentType,
            contentDescription = getFileNameFromUri(uri),
            processingStep = ProcessingStep.DETECTING_TYPE
        )

        viewModelScope.launch {
            when {
                mimeType.startsWith("image/") -> processImageFile(uri)
                mimeType.startsWith("audio/") -> processAudioFile(uri)
                mimeType.startsWith("text/") || mimeType == "application/pdf" -> processDocumentFile(uri, mimeType)
                else -> {
                    updateState(
                        processingStep = ProcessingStep.ERROR,
                        error = "Unsupported file type: $mimeType"
                    )
                    _processingResult.value = ShareProcessingResult(
                        success = false,
                        error = "Unsupported file type: $mimeType"
                    )
                }
            }
        }
    }

    fun processSharedFiles(uris: List<Uri>, mimeType: String) {
        Log.d(TAG, "Processing shared files: ${uris.size} files, MIME: $mimeType")
        updateState(
            contentType = "Multiple Files",
            contentDescription = "${uris.size} files",
            processingStep = ProcessingStep.DETECTING_TYPE
        )

        // For now, process only the first file as an example
        // In a full implementation, you might want to process all files or show a selection
        if (uris.isNotEmpty()) {
            processSharedFile(uris.first(), mimeType)
        }
    }

    private suspend fun processImageFile(uri: Uri) {
        updateState(processingStep = ProcessingStep.EXTRACTING_TEXT)

        imageTextExtractionService.extractTextFromUri(context, uri)
            .onSuccess { extractedText ->
                Log.d(TAG, "Image text extracted: ${extractedText.take(50)}...")
                updateState(processingStep = ProcessingStep.PROCESSING_TEXT)
                processSharedText(extractedText, "Image text")
            }
            .onFailure { exception ->
                Log.e(TAG, "Failed to extract text from image", exception)
                updateState(
                    processingStep = ProcessingStep.ERROR,
                    error = "Failed to extract text from image: ${exception.message}"
                )
                _processingResult.value = ShareProcessingResult(
                    success = false,
                    error = exception.message
                )
            }
    }

    private suspend fun processAudioFile(uri: Uri) {
        updateState(processingStep = ProcessingStep.TRANSCRIBING_AUDIO)

        try {
            val audioFile = createTempFileFromUri(uri)
            if (audioFile != null) {
                // Use the enhanced AudioTranscriptionProcessor instead of direct service
                audioTranscriptionProcessor.processAudioFile(
                    audioFile = audioFile,
                    progressCallback = { progress ->
                        // Map audio processing progress to share processing steps
                        val shareStep = when (progress.stage) {
                            ProcessingStage.ANALYZING_AUDIO -> ProcessingStep.TRANSCRIBING_AUDIO
                            ProcessingStage.SPLITTING_AUDIO -> ProcessingStep.TRANSCRIBING_AUDIO
                            ProcessingStage.TRANSCRIBING_SEGMENTS -> ProcessingStep.TRANSCRIBING_AUDIO
                            ProcessingStage.PROCESSING_TEXT -> ProcessingStep.PROCESSING_TEXT
                            ProcessingStage.COMPLETED -> ProcessingStep.PROCESSING_TEXT
                            ProcessingStage.ERROR -> ProcessingStep.ERROR
                        }
                        updateState(
                            processingStep = shareStep,
                            progress = when (progress.stage) {
                                ProcessingStage.ANALYZING_AUDIO -> 0.2f
                                ProcessingStage.SPLITTING_AUDIO -> 0.4f
                                ProcessingStage.TRANSCRIBING_SEGMENTS ->
                                    0.5f + (progress.current.toFloat() / progress.total.toFloat() * 0.2f)
                                ProcessingStage.PROCESSING_TEXT -> 0.8f
                                ProcessingStage.COMPLETED -> 1.0f
                                ProcessingStage.ERROR -> 0.0f
                            }
                        )
                    },
                    callback = { result ->
                        viewModelScope.launch {
                            result.onSuccess { transcription ->
                                Log.d(TAG, "Audio processed with enhanced pipeline: ${transcription.take(50)}...")
                                updateState(processingStep = ProcessingStep.PROCESSING_TEXT)
                                processSharedText(transcription, "Audio transcription")
                            }.onFailure { exception ->
                                Log.e(TAG, "Failed to process audio with enhanced pipeline", exception)
                                updateState(
                                    processingStep = ProcessingStep.ERROR,
                                    error = "Failed to process audio: ${exception.message}"
                                )
                                _processingResult.value = ShareProcessingResult(
                                    success = false,
                                    error = exception.message
                                )
                            }
                        }
                    }
                )
            } else {
                updateState(
                    processingStep = ProcessingStep.ERROR,
                    error = "Failed to process audio file"
                )
                _processingResult.value = ShareProcessingResult(
                    success = false,
                    error = "Failed to process audio file"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing audio file", e)
            updateState(
                processingStep = ProcessingStep.ERROR,
                error = "Error processing audio file: ${e.message}"
            )
            _processingResult.value = ShareProcessingResult(
                success = false,
                error = e.message
            )
        }
    }

    private suspend fun processDocumentFile(uri: Uri, mimeType: String) {
        updateState(processingStep = ProcessingStep.EXTRACTING_TEXT)

        try {
            Log.d(TAG, "Starting document processing for URI: $uri, MIME: $mimeType")

            val result = documentTextProcessingService.extractTextFromDocument(context, uri)
            Log.d(TAG, "Document extraction result: ${result.isSuccess}")

            result.onSuccess { extractedText ->
                Log.d(TAG, "Document extraction successful, text length: ${extractedText.length}")
                if (extractedText.isNotEmpty()) {
                    Log.d(TAG, "Document text extracted: ${extractedText.take(50)}...")
                    updateState(processingStep = ProcessingStep.PROCESSING_TEXT)
                    // Don't launch another coroutine here, just call the function
                    processSharedText(extractedText, "Document text")
                } else {
                    Log.w(TAG, "Document extraction returned empty text")
                    updateState(
                        processingStep = ProcessingStep.ERROR,
                        error = "No text found in document"
                    )
                    _processingResult.value = ShareProcessingResult(
                        success = false,
                        error = "No text found in document"
                    )
                }
            }
            .onFailure { exception ->
                Log.e(TAG, "Failed to extract text from document", exception)
                updateState(
                    processingStep = ProcessingStep.ERROR,
                    error = "Failed to extract text from document: ${exception.message}"
                )
                _processingResult.value = ShareProcessingResult(
                    success = false,
                    error = exception.message
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error in processDocumentFile", e)
            updateState(
                processingStep = ProcessingStep.ERROR,
                error = "Unexpected error processing document: ${e.message}"
            )
            _processingResult.value = ShareProcessingResult(
                success = false,
                error = e.message
            )
        }
    }

    private fun createTempFileFromUri(uri: Uri): File? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val tempFile = File.createTempFile("shared_audio", ".tmp", context.cacheDir)
            inputStream?.use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }
            tempFile
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create temp file from URI", e)
            null
        }
    }

    private fun determineContentType(mimeType: String): String {
        return when {
            mimeType.startsWith("image/") -> "Image"
            mimeType.startsWith("audio/") -> "Audio"
            mimeType.startsWith("text/") -> "Document"
            mimeType == "application/pdf" -> "PDF Document"
            else -> "File"
        }
    }

    private fun getFileNameFromUri(uri: Uri): String {
        return try {
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex("_display_name")
                    if (nameIndex != -1) {
                        return it.getString(nameIndex)
                    }
                }
            }
            "Shared file"
        } catch (e: Exception) {
            "Shared file"
        }
    }

    private fun updateState(
        contentType: String = _uiState.value.contentType,
        contentDescription: String = _uiState.value.contentDescription,
        processingStep: ProcessingStep = _uiState.value.processingStep,
        isLoading: Boolean = processingStep != ProcessingStep.IDLE && processingStep != ProcessingStep.COMPLETED && processingStep != ProcessingStep.ERROR,
        progress: Float = when (processingStep) {
            ProcessingStep.IDLE -> 0f
            ProcessingStep.DETECTING_TYPE -> 0.1f
            ProcessingStep.EXTRACTING_TEXT -> 0.3f
            ProcessingStep.TRANSCRIBING_AUDIO -> 0.5f
            ProcessingStep.PROCESSING_TEXT -> 0.7f
            ProcessingStep.SAVING_RESULTS -> 0.9f
            ProcessingStep.COMPLETED -> 1f
            ProcessingStep.ERROR -> 0f
        },
        error: String? = null,
        successMessage: String? = null
    ) {
        _uiState.value = ShareProcessingUiState(
            contentType = contentType,
            contentDescription = contentDescription,
            processingStep = processingStep,
            isLoading = isLoading,
            progress = progress,
            error = error,
            successMessage = successMessage
        )
    }

    fun setError(error: String) {
        updateState(
            processingStep = ProcessingStep.ERROR,
            error = error
        )
        _processingResult.value = ShareProcessingResult(
            success = false,
            error = error
        )
    }
}