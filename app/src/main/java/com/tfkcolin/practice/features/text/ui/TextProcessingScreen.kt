package com.tfkcolin.practice.features.text.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.text.domain.ProcessingStep
import com.tfkcolin.practice.features.text.domain.TextProcessingViewModel
import com.tfkcolin.practice.ui.components.ProfessionalButton
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TextProcessingScreen(
    extractedText: String,
    detectedLanguage: String?,
    inputType: String, // "image" or "document"
    onProcessingComplete: (String) -> Unit, // translationId
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: TextProcessingViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Start processing when screen loads
    LaunchedEffect(Unit) {
        when (inputType) {
            "image" -> viewModel.startImageProcessing(extractedText, detectedLanguage)
            "document" -> viewModel.startDocumentProcessing(extractedText, detectedLanguage)
        }
    }

    // Handle completion
    LaunchedEffect(uiState.currentStep, uiState.translation?.id) {
        if (uiState.currentStep == ProcessingStep.COMPLETED && uiState.translation != null) {
            onProcessingComplete(uiState.translation!!.id)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Processing Text",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = {
                            if (uiState.canCancel) {
                                viewModel.cancelProcessing()
                                onBackPressed()
                            }
                        },
                        enabled = uiState.canCancel
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = if (uiState.canCancel) PrimaryBlack else MediumGray
                        )
                    }
                },
                actions = {
                    if (uiState.canCancel) {
                        IconButton(onClick = { viewModel.cancelProcessing() }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Cancel",
                                tint = ErrorRed
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Progress indicator
            Box(
                modifier = Modifier.size(120.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    progress = { uiState.progress },
                    modifier = Modifier.fillMaxSize(),
                    color = AccentBlue,
                    strokeWidth = 4.dp
                )

                when (uiState.currentStep) {
                    ProcessingStep.IDLE -> Icon(
                        imageVector = Icons.Default.HourglassEmpty,
                        contentDescription = null,
                        tint = MediumGray,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.EXTRACTING_TEXT -> Icon(
                        imageVector = Icons.Default.Image,
                        contentDescription = null,
                        tint = AccentBlue,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.DETECTING_LANGUAGE -> Icon(
                        imageVector = Icons.Default.Language,
                        contentDescription = null,
                        tint = AccentGreen,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.SPLITTING_TEXT -> Icon(
                        imageVector = Icons.Default.ContentCut,
                        contentDescription = null,
                        tint = AccentOrange,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.PROCESSING_AI -> Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = null,
                        tint = AccentPurple,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.SAVING_RESULTS -> Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = null,
                        tint = AccentGreen,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.COMPLETED -> Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = SuccessGreen,
                        modifier = Modifier.size(48.dp)
                    )
                    ProcessingStep.ERROR -> Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = ErrorRed,
                        modifier = Modifier.size(48.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Current step description
            Text(
                text = viewModel.getProgressMessage(),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Progress text
            Text(
                text = "${(uiState.progress * 100).toInt()}% complete",
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                textAlign = TextAlign.Center
            )

            // Error state
            if (uiState.currentStep == ProcessingStep.ERROR) {
                Spacer(modifier = Modifier.height(24.dp))

                Card(
                    colors = CardDefaults.cardColors(containerColor = ErrorRed.copy(alpha = 0.1f)),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = null,
                                tint = ErrorRed,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Processing Failed",
                                style = MaterialTheme.typography.titleMedium,
                                color = ErrorRed,
                                fontWeight = FontWeight.SemiBold
                            )
                        }

                        uiState.error?.let { error ->
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = error,
                                style = MaterialTheme.typography.bodyMedium,
                                color = PrimaryBlack
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    ProfessionalButton(
                        text = "Try Again",
                        onClick = { viewModel.retryProcessing() },
                        modifier = Modifier.weight(1f)
                    )

                    ProfessionalButton(
                        text = "Go Back",
                        onClick = onBackPressed,
                        modifier = Modifier.weight(1f),
                        backgroundColor = MediumGray
                    )
                }
            }

            // Success state
            if (uiState.currentStep == ProcessingStep.COMPLETED) {
                Spacer(modifier = Modifier.height(24.dp))

                Card(
                    colors = CardDefaults.cardColors(containerColor = SuccessGreen.copy(alpha = 0.1f)),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = SuccessGreen,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Translation completed successfully!",
                            style = MaterialTheme.typography.titleMedium,
                            color = SuccessGreen,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                ProfessionalButton(
                    text = "View Results",
                    onClick = {
                        uiState.translation?.let { translation ->
                            onProcessingComplete(translation.id)
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = AccentBlue
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            // Input type info
            if (uiState.currentStep != ProcessingStep.ERROR && uiState.currentStep != ProcessingStep.COMPLETED) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = LightGray)
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = if (inputType == "image") Icons.Default.Image else Icons.Default.Description,
                            contentDescription = null,
                            tint = AccentBlue,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Processing ${inputType.replaceFirstChar { it.uppercase() }} input",
                            style = MaterialTheme.typography.bodySmall,
                            color = DarkGray
                        )
                    }
                }
            }
        }
    }
}