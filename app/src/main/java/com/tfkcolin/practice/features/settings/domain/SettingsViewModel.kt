package com.tfkcolin.practice.features.settings.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.language.domain.LanguageManager
import com.tfkcolin.practice.features.language.domain.SupportedLanguage
import com.tfkcolin.practice.features.text.data.LanguageValidator
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

data class SettingsUiState(
    val availableLanguages: List<SupportedLanguage> = emptyList(),
    val selectedLanguage: SupportedLanguage? = null,
    val availableTargetLanguages: List<String> = emptyList(),
    val selectedTargetLanguage: String = "en",
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val languageManager: LanguageManager,
    private val preferencesRepository: PreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        initializeLanguages()
        loadTargetLanguageSettings()
    }

    private fun initializeLanguages() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                languageManager.initializeLanguages()

                // Collect language manager state
                languageManager.state.collectLatest { languageState ->
                    _uiState.value = _uiState.value.copy(
                        availableLanguages = languageState.supportedLanguages,
                        selectedLanguage = languageState.selectedLanguage,
                        isLoading = languageState.isInitializing,
                        error = languageState.error
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load settings: ${e.message}"
                )
            }
        }
    }

    private fun loadTargetLanguageSettings() {
        val userPrefs = preferencesRepository.getCurrentPreferences()
        val availableTargets = LanguageValidator.getSupportedTargetLanguages().map { languageCode ->
            LanguageValidator.getDisplayName(languageCode)
        }

        _uiState.value = _uiState.value.copy(
            availableTargetLanguages = availableTargets,
            selectedTargetLanguage = userPrefs.selectedTargetLanguage
        )
    }

    fun retryInitialization() {
        initializeLanguages()
    }

    fun selectLanguage(language: SupportedLanguage) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                languageManager.selectLanguage(language)
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to change language: ${e.message}"
                )
            }
        }
    }

    fun selectTargetLanguage(targetLanguage: String) {
        preferencesRepository.saveTargetLanguage(targetLanguage)
        _uiState.value = _uiState.value.copy(selectedTargetLanguage = targetLanguage)
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}