package com.tfkcolin.practice.features.practice.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.language.domain.LanguageManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class PracticeUiState(
    val practiceText: String = "¡Hola, Isa!",
    val translationText: String = "Hi, Isa!",
    val currentStep: Int = 1,
    val totalSteps: Int = 3,
    val isSubwayMode: Boolean = true,
    val isPlaying: Boolean = false,
    val isListening: Boolean = false,
    val recognizedText: String = "",
    val statusMessage: String = "",
    val isEngineReady: Boolean = false
)

@HiltViewModel
class PracticeViewModel @Inject constructor(
    private val languageManager: LanguageManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(PracticeUiState())
    val uiState: StateFlow<PracticeUiState> = _uiState

    private val practiceTexts = listOf(
        "¡Hola, Isa!" to "Hi, Isa!",
        "¿Cómo estás?" to "How are you?",
        "Muy bien, gracias" to "Very well, thank you"
    )

    init {
        // Check if engines are ready
        viewModelScope.launch {
            languageManager.state.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isEngineReady = state.isReady,
                    statusMessage = state.error ?: ""
                )
            }
        }
        
        // Set initial practice text
        updatePracticeText()
    }

    private fun updatePracticeText() {
        val currentIndex = (_uiState.value.currentStep - 1) % practiceTexts.size
        val (text, translation) = practiceTexts[currentIndex]
        _uiState.value = _uiState.value.copy(
            practiceText = text,
            translationText = translation
        )
    }

    fun playAudio() {
        if (_uiState.value.isEngineReady) {
            _uiState.value = _uiState.value.copy(isPlaying = true)
            
            val ttsRepository = languageManager.getCurrentTtsRepository()
            ttsRepository.speak(_uiState.value.practiceText)
            
            // Simulate audio completion (in real app, you'd listen to TTS completion)
            viewModelScope.launch {
                kotlinx.coroutines.delay(2000) // Simulate audio duration
                _uiState.value = _uiState.value.copy(isPlaying = false)
            }
        }
    }

    fun pauseAudio() {
        _uiState.value = _uiState.value.copy(isPlaying = false)
        // In real implementation, you'd pause the TTS
    }

    fun startListening() {
        if (_uiState.value.isEngineReady && !_uiState.value.isListening) {
            _uiState.value = _uiState.value.copy(isListening = true, recognizedText = "")
            
            val speechRepository = languageManager.getCurrentSpeechRepository()
            speechRepository.startListening(
                callback = { text ->
                    _uiState.value = _uiState.value.copy(
                        recognizedText = text,
                        isListening = speechRepository.isListening
                    )
                },
                errorCallback = { error ->
                    _uiState.value = _uiState.value.copy(
                        statusMessage = error,
                        isListening = false
                    )
                }
            )
        }
    }

    fun stopListening() {
        val speechRepository = languageManager.getCurrentSpeechRepository()
        speechRepository.stopListening()
        _uiState.value = _uiState.value.copy(isListening = false)
    }

    fun nextStep() {
        val nextStep = if (_uiState.value.currentStep < _uiState.value.totalSteps) {
            _uiState.value.currentStep + 1
        } else {
            1 // Loop back to first step
        }
        
        _uiState.value = _uiState.value.copy(
            currentStep = nextStep,
            recognizedText = ""
        )
        updatePracticeText()
    }

    fun restart() {
        _uiState.value = _uiState.value.copy(
            currentStep = 1,
            recognizedText = "",
            isPlaying = false,
            isListening = false
        )
        updatePracticeText()
    }

    fun toggleSubwayMode() {
        _uiState.value = _uiState.value.copy(
            isSubwayMode = !_uiState.value.isSubwayMode
        )
    }
}
