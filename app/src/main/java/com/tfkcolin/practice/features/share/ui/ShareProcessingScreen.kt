package com.tfkcolin.practice.features.share.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.share.data.ProcessingStep
import com.tfkcolin.practice.features.share.domain.ShareProcessingViewModel
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShareProcessingScreen(
    modifier: Modifier = Modifier,
    viewModel: ShareProcessingViewModel,
    onProcessingComplete: (String) -> Unit,
    onError: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val processingResult by viewModel.processingResult.collectAsStateWithLifecycle()

    // Handle processing completion
    LaunchedEffect(processingResult) {
        processingResult?.let { result ->
            if (result.success && result.translationId != null) {
                // Add longer delay to show success message before navigating
                kotlinx.coroutines.delay(5000) // 5 seconds for success
                onProcessingComplete(result.translationId)
            } else if (!result.success) {
                // Add delay for error message too
                kotlinx.coroutines.delay(8000) // 8 seconds for errors to ensure user can read
                onError()
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Processing Shared Content",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Content type icon
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(
                        when (uiState.contentType) {
                            "Text" -> AccentBlue.copy(alpha = 0.2f)
                            "Image" -> SuccessGreen.copy(alpha = 0.2f)
                            "Audio" -> AccentBlue.copy(alpha = 0.2f)
                            "Document", "PDF Document" -> DarkGray.copy(alpha = 0.2f)
                            else -> LightGray
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = when (uiState.contentType) {
                        "Text" -> Icons.Default.TextFields
                        "Image" -> Icons.Default.Image
                        "Audio" -> Icons.Default.AudioFile
                        "Document", "PDF Document" -> Icons.Default.Description
                        else -> Icons.Default.Share
                    },
                    contentDescription = null,
                    tint = when (uiState.contentType) {
                        "Text" -> AccentBlue
                        "Image" -> SuccessGreen
                        "Audio" -> AccentBlue
                        "Document", "PDF Document" -> PrimaryBlack
                        else -> PrimaryBlack
                    },
                    modifier = Modifier.size(40.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Content type and description
            Text(
                text = uiState.contentType,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = uiState.contentDescription,
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(48.dp))

            // Processing status
            when (uiState.processingStep) {
                ProcessingStep.IDLE -> {
                    Text(
                        text = "Preparing to process...",
                        style = MaterialTheme.typography.bodyLarge,
                        color = DarkGray
                    )
                }
                ProcessingStep.DETECTING_TYPE -> {
                    ProcessingIndicator("Detecting content type...")
                }
                ProcessingStep.EXTRACTING_TEXT -> {
                    ProcessingIndicator("Extracting text...")
                }
                ProcessingStep.TRANSCRIBING_AUDIO -> {
                    ProcessingIndicator("Transcribing audio...")
                }
                ProcessingStep.PROCESSING_TEXT -> {
                    ProcessingIndicator("Processing with AI...")
                }
                ProcessingStep.SAVING_RESULTS -> {
                    ProcessingIndicator("Saving results...")
                }
                ProcessingStep.COMPLETED -> {
                    SuccessIndicator("✓ Translation Complete!\nNavigating to results...")
                }
                ProcessingStep.ERROR -> {
                    ErrorIndicator(uiState.error ?: "An error occurred")
                }
            }

            // Progress bar
            if (uiState.isLoading && uiState.processingStep != ProcessingStep.ERROR) {
                Spacer(modifier = Modifier.height(24.dp))
                LinearProgressIndicator(
                    progress = { uiState.progress },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(4.dp)
                        .clip(RoundedCornerShape(2.dp)),
                    color = AccentBlue,
                    trackColor = LightGray
                )
            }

            // Success message
            uiState.successMessage?.let { message ->
                Spacer(modifier = Modifier.height(24.dp))
                Card(
                    colors = CardDefaults.cardColors(containerColor = SuccessGreen.copy(alpha = 0.1f)),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = SuccessGreen,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = message,
                            style = MaterialTheme.typography.bodyMedium,
                            color = SuccessGreen,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            // Error message
            uiState.error?.let { error ->
                Spacer(modifier = Modifier.height(24.dp))
                Card(
                    colors = CardDefaults.cardColors(containerColor = ErrorRed.copy(alpha = 0.1f)),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = ErrorRed,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "Processing Failed",
                                style = MaterialTheme.typography.titleMedium,
                                color = ErrorRed,
                                fontWeight = FontWeight.SemiBold
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = error,
                                style = MaterialTheme.typography.bodyMedium,
                                color = PrimaryBlack
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProcessingIndicator(message: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(24.dp),
            color = AccentBlue,
            strokeWidth = 2.dp
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = PrimaryBlack
        )
    }
}

@Composable
private fun SuccessIndicator(message: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = null,
            tint = SuccessGreen,
            modifier = Modifier.size(32.dp)
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = SuccessGreen,
            fontWeight = FontWeight.Medium,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center,
            lineHeight = 20.sp
        )
    }
}

@Composable
private fun ErrorIndicator(message: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            tint = ErrorRed,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = ErrorRed,
            fontWeight = FontWeight.Medium
        )
    }
}