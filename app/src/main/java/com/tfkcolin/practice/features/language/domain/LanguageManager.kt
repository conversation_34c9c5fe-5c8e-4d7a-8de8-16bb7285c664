package com.tfkcolin.practice.features.language.domain

import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.speech.data.SpeechRepository
import com.tfkcolin.practice.features.tts.data.TtsRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

data class SupportedLanguage(
    val locale: Locale,
    val displayName: String,
    val ttsEngineKey: String,
    val speechEngineKey: String,
    val isAvailable: Boolean = true
)

data class LanguageManagerState(
    val supportedLanguages: List<SupportedLanguage> = emptyList(),
    val selectedLanguage: SupportedLanguage? = null,
    val isInitializing: Boolean = false,
    val isReady: Boolean = false,
    val error: String? = null
)

@Singleton
class LanguageManager @Inject constructor(
    private val ttsRepository: TtsRepository,
    private val speechRepository: SpeechRepository,
    private val preferencesRepository: PreferencesRepository
) {
    private val _state = MutableStateFlow(LanguageManagerState())
    val state: StateFlow<LanguageManagerState> = _state

    // Engine priority mapping - which engines to prefer for each language
    private val enginePreferences = mapOf(
        // English - prefer Android TTS and Speech
        "en" to Pair("android", "android"),
        // Spanish - prefer Android TTS and Speech
        "es" to Pair("android", "android"),
        // German - prefer Google TTS if available, Android Speech
        "de" to Pair("google", "android"),
        // French - prefer Google TTS if available, Android Speech
        "fr" to Pair("google", "android"),
        // Japanese - prefer Google TTS if available, Android Speech
        "ja" to Pair("google", "android"),
        // Chinese - prefer Google TTS if available, Android Speech
        "zh" to Pair("google", "android")
    )

    suspend fun initializeLanguages() {
        _state.value = _state.value.copy(isInitializing = true, error = null)
        
        try {
            val supportedLanguages = mutableListOf<SupportedLanguage>()
            
            // Get available engines
            val ttsEngines = mapOf(
                "android" to "System (Android)",
                "google" to "Google Cloud"
            )
            
            val speechEngines = mapOf(
                "android" to "System (Android)"
            )
            
            // For each engine combination, find common languages
            for ((ttsKey, ttsName) in ttsEngines) {
                for ((speechKey, speechName) in speechEngines) {
                    val commonLanguages = findCommonLanguages(ttsKey, speechKey)
                    
                    commonLanguages.forEach { locale ->
                        // Check if we already have this language with a better engine combination
                        val existing = supportedLanguages.find { it.locale.language == locale.language }
                        val preferred = enginePreferences[locale.language] ?: Pair("android", "android")
                        
                        if (existing == null || 
                            (ttsKey == preferred.first && speechKey == preferred.second)) {
                            
                            // Remove existing if we found a better combination
                            if (existing != null) {
                                supportedLanguages.remove(existing)
                            }
                            
                            supportedLanguages.add(
                                SupportedLanguage(
                                    locale = locale,
                                    displayName = locale.getDisplayName(locale),
                                    ttsEngineKey = ttsKey,
                                    speechEngineKey = speechKey,
                                    isAvailable = true
                                )
                            )
                        }
                    }
                }
            }
            
            // Sort by display name
            val sortedLanguages = supportedLanguages.sortedBy { it.displayName }
            
            // Check if user has a previously selected language
            val savedPreferences = preferencesRepository.getCurrentPreferences()
            val savedLanguage = savedPreferences.toSupportedLanguage()

            // Use saved language if it exists and is available, otherwise use default
            val selectedLanguage = if (savedLanguage != null &&
                                      sortedLanguages.any { it.locale.language == savedLanguage.locale.language }) {
                savedLanguage
            } else {
                // Set default language (English if available, otherwise first available)
                sortedLanguages.find { it.locale.language == "en" } ?: sortedLanguages.firstOrNull()
            }

            _state.value = _state.value.copy(
                supportedLanguages = sortedLanguages,
                selectedLanguage = selectedLanguage,
                isInitializing = false,
                isReady = true
            )

            // Configure engines for selected language
            selectedLanguage?.let { selectLanguage(it) }
            
        } catch (e: Exception) {
            _state.value = _state.value.copy(
                isInitializing = false,
                error = "Failed to initialize languages: ${e.message}"
            )
        }
    }
    
    private suspend fun findCommonLanguages(ttsEngineKey: String, speechEngineKey: String): List<Locale> {
        return try {
            // Initialize TTS engine and get its languages
            var ttsLanguages: List<Locale> = emptyList()
            var ttsInitialized = false

            ttsRepository.chooseEngine(ttsEngineKey) { success ->
                if (success) {
                    ttsLanguages = ttsRepository.getAvailableLanguages()
                }
                ttsInitialized = true
            }

            // Wait for TTS initialization (simple busy wait for now)
            var attempts = 0
            while (!ttsInitialized && attempts < 50) {
                kotlinx.coroutines.delay(100)
                attempts++
            }

            // Initialize Speech engine and get its languages
            var speechLanguages: List<Locale> = emptyList()
            var speechInitialized = false

            speechRepository.chooseEngine(speechEngineKey) { success ->
                if (success) {
                    speechLanguages = speechRepository.getAvailableLanguages()
                }
                speechInitialized = true
            }

            // Wait for Speech initialization
            attempts = 0
            while (!speechInitialized && attempts < 50) {
                kotlinx.coroutines.delay(100)
                attempts++
            }

            // Find intersection based on language code (ignore country variants for now)
            val ttsLanguageCodes = ttsLanguages.map { it.language }.toSet()
            val speechLanguageCodes = speechLanguages.map { it.language }.toSet()
            val commonLanguageCodes = ttsLanguageCodes.intersect(speechLanguageCodes)

            // Return one locale per language code (prefer the TTS engine's locale)
            commonLanguageCodes.mapNotNull { languageCode ->
                ttsLanguages.find { it.language == languageCode }
                    ?: speechLanguages.find { it.language == languageCode }
            }

        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun selectLanguage(language: SupportedLanguage) {
        _state.value = _state.value.copy(isInitializing = true)

        try {
            var ttsReady = false
            var speechReady = false
            var ttsInitialized = false
            var speechInitialized = false

            // Configure TTS engine
            ttsRepository.chooseEngine(language.ttsEngineKey) { success ->
                ttsReady = success
                if (success) {
                    ttsRepository.setLanguage(language.locale)
                }
                ttsInitialized = true
            }

            // Wait for TTS initialization
            var attempts = 0
            while (!ttsInitialized && attempts < 50) {
                kotlinx.coroutines.delay(100)
                attempts++
            }

            // Configure Speech engine
            speechRepository.chooseEngine(language.speechEngineKey) { success ->
                speechReady = success
                if (success) {
                    speechRepository.setLanguage(language.locale)
                }
                speechInitialized = true
            }

            // Wait for Speech initialization
            attempts = 0
            while (!speechInitialized && attempts < 50) {
                kotlinx.coroutines.delay(100)
                attempts++
            }

            if (ttsReady && speechReady) {
                _state.value = _state.value.copy(
                    selectedLanguage = language,
                    isInitializing = false,
                    isReady = true,
                    error = null
                )
                // Save the selected language to preferences
                preferencesRepository.saveSelectedLanguage(language)
            } else {
                _state.value = _state.value.copy(
                    isInitializing = false,
                    error = "Failed to configure engines for ${language.displayName}"
                )
            }

        } catch (e: Exception) {
            _state.value = _state.value.copy(
                isInitializing = false,
                error = "Error selecting language: ${e.message}"
            )
        }
    }
    
    fun getCurrentTtsRepository(): TtsRepository = ttsRepository
    fun getCurrentSpeechRepository(): SpeechRepository = speechRepository
}
