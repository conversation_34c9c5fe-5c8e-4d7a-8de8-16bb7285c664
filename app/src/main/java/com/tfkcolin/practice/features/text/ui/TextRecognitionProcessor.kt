package com.tfkcolin.practice.features.text.ui

import android.graphics.Rect
import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions

data class RecognizedTextBlock(
    val text: String,
    val boundingBox: Rect,
    val isSelected: Boolean = false
)

class TextRecognitionProcessor {
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

    @OptIn(ExperimentalGetImage::class)
    fun processImage(
        imageProxy: ImageProxy,
        onSuccess: (List<RecognizedTextBlock>) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)

            textRecognizer.process(image)
                .addOnSuccessListener { visionText ->
                    val textBlocks = visionText.textBlocks.map { textBlock ->
                        RecognizedTextBlock(
                            text = textBlock.text,
                            boundingBox = textBlock.boundingBox ?: Rect()
                        )
                    }
                    onSuccess(textBlocks)
                    imageProxy.close()
                }
                .addOnFailureListener { e ->
                    onFailure(e)
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }

    fun processImage(
        bitmap: android.graphics.Bitmap,
        onSuccess: (String) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        val image = InputImage.fromBitmap(bitmap, 0)

        textRecognizer.process(image)
            .addOnSuccessListener { visionText ->
                val extractedText = visionText.text
                onSuccess(extractedText)
            }
            .addOnFailureListener { e ->
                onFailure(e)
            }
    }

    fun close() {
        textRecognizer.close()
    }
}