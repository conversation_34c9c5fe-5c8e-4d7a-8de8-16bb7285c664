package com.tfkcolin.practice.features.text.ui

import android.graphics.Bitmap
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import kotlinx.coroutines.launch

private const val TAG = "CameraXCamera"

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraXCamera(
    onImageCaptured: (Bitmap) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val configuration = LocalConfiguration.current
    val coroutineScope = rememberCoroutineScope()

    // Get current device rotation
    val rotation = remember(configuration.orientation) {
        when (configuration.orientation) {
            android.content.res.Configuration.ORIENTATION_PORTRAIT -> android.view.Surface.ROTATION_0
            android.content.res.Configuration.ORIENTATION_LANDSCAPE -> android.view.Surface.ROTATION_90
            else -> android.view.Surface.ROTATION_0
        }
    }

    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val imageCapture = remember(rotation) {
        ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .setTargetRotation(rotation)
            .build()
    }

    val preview = remember(rotation) {
        Preview.Builder()
            .setTargetRotation(rotation)
            .build()
    }

    var isCapturing by remember { mutableStateOf(false) }

    AndroidView(
        modifier = modifier,
        factory = { ctx ->
            PreviewView(ctx).apply {
                layoutParams = android.view.ViewGroup.LayoutParams(
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                    android.view.ViewGroup.LayoutParams.MATCH_PARENT
                )
                scaleType = PreviewView.ScaleType.FILL_START
            }
        },
        update = { previewView ->
            cameraProviderFuture.addListener({
                try {
                    val cameraProvider = cameraProviderFuture.get()

                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll()

                    // Bind use cases to camera
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        CameraSelector.DEFAULT_BACK_CAMERA,
                        preview,
                        imageCapture
                    )

                    // Attach preview to preview view
                    preview.surfaceProvider = previewView.surfaceProvider
                } catch (exc: Exception) {
                    Log.e(TAG, "Use case binding failed", exc)
                }
            }, ContextCompat.getMainExecutor(context))
        }
    )

    // Capture button overlay
    Box(modifier = modifier) {
        // Camera controls
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Capture button
            FloatingActionButton(
                onClick = {
                    if (!isCapturing) {
                        isCapturing = true
                        coroutineScope.launch {
                            try {
                                val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
                                    java.io.File(context.cacheDir, "temp_capture.jpg")
                                ).build()

                                imageCapture.takePicture(
                                    outputFileOptions,
                                    ContextCompat.getMainExecutor(context),
                                    object : ImageCapture.OnImageSavedCallback {
                                        override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                                            try {
                                                val bitmap = android.graphics.BitmapFactory.decodeFile(
                                                    output.savedUri?.path
                                                        ?: (context.cacheDir.path + "/temp_capture.jpg")
                                                )
                                                onImageCaptured(bitmap)
                                            } catch (e: Exception) {
                                                Log.e(TAG, "Failed to decode captured image", e)
                                            } finally {
                                                isCapturing = false
                                            }
                                        }

                                        override fun onError(exception: ImageCaptureException) {
                                            Log.e(TAG, "Photo capture failed: ${exception.message}", exception)
                                            isCapturing = false
                                        }
                                    }
                                )
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to capture image", e)
                                isCapturing = false
                            }
                        }
                    }
                },
                modifier = Modifier.size(72.dp)
            ) {
                if (isCapturing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = androidx.compose.ui.graphics.Color.White
                    )
                } else {
                    Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Camera,
                        contentDescription = "Take Photo"
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Cancel button
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    }
}
