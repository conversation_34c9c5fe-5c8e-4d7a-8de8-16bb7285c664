package com.tfkcolin.practice.features.text.ui

import android.Manifest
import android.content.pm.PackageManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import com.tfkcolin.practice.features.text.domain.ImageCaptureViewModel
import com.tfkcolin.practice.features.text.ui.CameraXCamera
import com.tfkcolin.practice.ui.components.ProfessionalButton
import com.tfkcolin.practice.ui.theme.*
import java.util.concurrent.Executors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageCaptureScreen(
    modifier: Modifier = Modifier,
    onImageProcessed: (String, String?, String?) -> Unit, // extractedText, detectedLanguage, sourceUri
    onBackPressed: () -> Unit,
    viewModel: ImageCaptureViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // Camera state
    var showCamera by remember { mutableStateOf(false) }
    var hasCameraPermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        )
    }

    // Gallery picker launcher
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { viewModel.onImageSelected(it) }
    }

    // Camera permission launcher
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
        if (isGranted) {
            showCamera = true
        }
    }

    // Handle successful text extraction
    LaunchedEffect(uiState.extractedText) {
        uiState.extractedText?.let { text ->
            onImageProcessed(text, uiState.detectedLanguage, uiState.selectedImageUri?.toString())
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Select Image",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = PrimaryBlack
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(32.dp))

            // Header text
            Text(
                text = "Choose an image",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "Select or take a photo containing text you want to extract and translate",
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 48.dp)
            )

            // Loading state
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = AccentBlue,
                    modifier = Modifier.size(48.dp)
                )
                Text(
                    text = "Extracting text from image...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = DarkGray,
                    modifier = Modifier.padding(top = 16.dp)
                )
            } else {
                // Image preview if selected
                uiState.selectedImageUri?.let { uri ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp)
                            .padding(bottom = 24.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        AsyncImage(
                            model = uri,
                            contentDescription = "Selected image",
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }

                // Error message
                uiState.error?.let { error ->
                    Card(
                        colors = CardDefaults.cardColors(containerColor = ErrorRed.copy(alpha = 0.1f)),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 24.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = null,
                                tint = ErrorRed,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = error,
                                style = MaterialTheme.typography.bodyMedium,
                                color = ErrorRed
                            )
                        }
                    }
                }

                // Success message with extracted text preview
                uiState.extractedText?.let { text ->
                    Card(
                        colors = CardDefaults.cardColors(containerColor = SuccessGreen.copy(alpha = 0.1f)),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 24.dp)
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = null,
                                    tint = SuccessGreen,
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(12.dp))
                                Text(
                                    text = "Text extracted successfully!",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = SuccessGreen,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = "\"${text.take(100)}${if (text.length > 100) "..." else ""}\"",
                                style = MaterialTheme.typography.bodyMedium,
                                color = PrimaryBlack
                            )

                            uiState.detectedLanguage?.let { lang ->
                                Text(
                                    text = "Detected language: $lang",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = DarkGray,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                    }
                }

                // Action buttons
                if (uiState.extractedText == null) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        ProfessionalButton(
                            text = "Select from Gallery",
                            onClick = { galleryLauncher.launch("image/*") },
                            modifier = Modifier.fillMaxWidth()
                        )

                        ProfessionalButton(
                            text = "Take Photo",
                            onClick = {
                                if (hasCameraPermission) {
                                    showCamera = true
                                } else {
                                    cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                            backgroundColor = AccentGreen
                        )
                    }
                }

                // Continue button when text is extracted
                uiState.extractedText?.let { _ ->
                    Spacer(modifier = Modifier.height(24.dp))

                    ProfessionalButton(
                        text = "Continue to Validation",
                        onClick = {
                            onImageProcessed(uiState.extractedText!!, uiState.detectedLanguage, uiState.selectedImageUri?.toString())
                        },
                        modifier = Modifier.fillMaxWidth(),
                        backgroundColor = AccentBlue
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            // Help text
            if (!uiState.isLoading && uiState.extractedText == null) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = LightGray)
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.TipsAndUpdates,
                            contentDescription = null,
                            tint = AccentBlue,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "For best results, use clear, well-lit images with large, readable text.",
                            style = MaterialTheme.typography.bodySmall,
                            color = DarkGray,
                            lineHeight = 16.sp
                        )
                    }
                }
            }
        }
    }

    // Show camera screen when requested
    if (showCamera) {
        CameraScreen(
            onImageCaptured = { bitmap ->
                viewModel.onImageCaptured(bitmap)
                showCamera = false
            },
            onDismiss = { showCamera = false }
        )
    }
}

@Composable
private fun CameraScreen(
    onImageCaptured: (android.graphics.Bitmap) -> Unit,
    onDismiss: () -> Unit
) {
    CameraXCamera(
        onImageCaptured = onImageCaptured,
        onDismiss = onDismiss
    )
}