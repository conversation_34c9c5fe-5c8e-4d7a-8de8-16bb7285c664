package com.tfkcolin.practice.features.text.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.text.domain.TranslationResultsViewModel
import com.tfkcolin.practice.ui.components.ProfessionalButton
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TranslationResultsScreen(
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: TranslationResultsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Translation Results",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = PrimaryBlack
                        )
                    }
                },
                actions = {
                    uiState.translation?.let { translation ->
                        IconButton(onClick = { viewModel.toggleStarTranslation() }) {
                            Icon(
                                imageVector = if (translation.isStarred) Icons.Default.Star else Icons.Default.StarBorder,
                                contentDescription = if (translation.isStarred) "Unstar" else "Star",
                                tint = if (translation.isStarred) AccentYellow else MediumGray
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        when {
            uiState.isLoading -> {
                Box(
                    modifier = modifier
                        .fillMaxSize()
                        .background(PrimaryWhite)
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = AccentBlue)
                }
            }
            uiState.error != null -> {
                Column(
                    modifier = modifier
                        .fillMaxSize()
                        .background(PrimaryWhite)
                        .padding(paddingValues),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = ErrorRed,
                        modifier = Modifier.size(64.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Failed to load translation",
                        style = MaterialTheme.typography.headlineSmall,
                        color = ErrorRed,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = uiState.error!!,
                        style = MaterialTheme.typography.bodyMedium,
                        color = DarkGray,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                    ProfessionalButton(
                        text = "Try Again",
                        onClick = { viewModel.retry() }
                    )
                }
            }
            uiState.translation != null -> {
                val translation = uiState.translation

                LazyColumn(
                    modifier = modifier
                        .fillMaxSize()
                        .background(PrimaryWhite)
                        .padding(paddingValues),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Original text
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = LightGray)
                        ) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text(
                                    text = "Original Text",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.SemiBold,
                                    color = PrimaryBlack,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                Text(
                                    text = translation?.originalText ?: "Not provided",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = DarkGray
                                )
                                translation?.originalTextPronunciations?.let { pronunciation ->
                                    Text(
                                        text = "Pronunciation: $pronunciation",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MediumGray,
                                        modifier = Modifier.padding(top = 4.dp)
                                    )
                                }
                            }
                        }
                    }

                    // Translated text
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = AccentBlue.copy(alpha = 0.1f))
                        ) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text(
                                    text = "Translation (${translation?.targetLanguage})",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.SemiBold,
                                    color = PrimaryBlack,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                Text(
                                    text = translation?.translatedText ?: "Not provided",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = PrimaryBlack
                                )
                            }
                        }
                    }

                    // Words section
                    if (translation?.words?.isNotEmpty() ?:  false) {
                        item {
                            Text(
                                text = "Vocabulary (${translation.words.size} words)",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.SemiBold,
                                color = PrimaryBlack,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }

                        items(translation.words) { word ->
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(containerColor = PrimaryWhite),
                                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                            ) {
                                Column(modifier = Modifier.padding(16.dp)) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = word.word,
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.SemiBold,
                                            color = PrimaryBlack
                                        )
                                        Badge(
                                            content = {
                                                Text(
                                                    text = "${viewModel.getWordFrequency(word.normalizedWord)}x",
                                                    style = MaterialTheme.typography.bodySmall
                                                )
                                            },
                                            containerColor = AccentBlue
                                        )
                                    }

                                    word.pronunciation?.let { pronunciation ->
                                        Text(
                                            text = pronunciation,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MediumGray,
                                            modifier = Modifier.padding(vertical = 4.dp)
                                        )
                                    }

                                    if (word.translations.isNotEmpty()) {
                                        Text(
                                            text = "Translations: ${word.translations.joinToString(", ")}",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = DarkGray,
                                            modifier = Modifier.padding(top = 4.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // Expressions section
                    if (translation?.expressions?.isNotEmpty() ?: false) {
                        item {
                            Text(
                                text = "Expressions (${translation.expressions.size} phrases)",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.SemiBold,
                                color = PrimaryBlack,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }

                        items(translation.expressions) { expression ->
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(containerColor = PrimaryWhite),
                                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                            ) {
                                Column(modifier = Modifier.padding(16.dp)) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = expression.sourceExpression,
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.SemiBold,
                                            color = PrimaryBlack
                                        )
                                        Badge(
                                            content = {
                                                Text(
                                                    text = "${viewModel.getExpressionFrequency(expression.normalizedExpression)}x",
                                                    style = MaterialTheme.typography.bodySmall
                                                )
                                            },
                                            containerColor = AccentGreen
                                        )
                                    }

                                    Text(
                                        text = expression.translatedExpression,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = DarkGray,
                                        modifier = Modifier.padding(vertical = 4.dp)
                                    )

                                    expression.pronunciation?.let { pronunciation ->
                                        Text(
                                            text = pronunciation,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MediumGray
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // Frequency rankings section
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = AccentYellow.copy(alpha = 0.1f))
                        ) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text(
                                    text = "Your Most Frequent Words",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.SemiBold,
                                    color = PrimaryBlack,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )

                                val topWords = viewModel.getTopWords(5)
                                if (topWords.isEmpty()) {
                                    Text(
                                        text = "No frequency data yet. Keep translating to see your most encountered words!",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = DarkGray
                                    )
                                } else {
                                    topWords.forEach { wordStat ->
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 4.dp),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Text(
                                                text = wordStat.normalizedWord,
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = PrimaryBlack
                                            )
                                            Text(
                                                text = "${wordStat.encounterCount} times",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = AccentBlue,
                                                fontWeight = FontWeight.SemiBold
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Metadata
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = LightGray)
                        ) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text(
                                    text = "Translation Details",
                                    style = MaterialTheme.typography.titleSmall,
                                    fontWeight = FontWeight.SemiBold,
                                    color = PrimaryBlack,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Language: ${translation?.sourceLanguage} → ${translation?.targetLanguage}",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = DarkGray
                                    )
                                    Text(
                                        text = "Source: ${translation?.source}",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = DarkGray
                                    )
                                }
                                Text(
                                    text = "Created: ${translation?.timestamp}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MediumGray,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}