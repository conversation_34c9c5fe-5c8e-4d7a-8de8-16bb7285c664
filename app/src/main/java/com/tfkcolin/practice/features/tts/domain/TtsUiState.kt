package com.tfkcolin.practice.features.tts.domain

import java.util.Locale

data class TtsUiState(
    val inputText: String = "",
    val selectedEngine: String = "android",
    val isInitializing: Boolean = false,
    val isEngineReady: Boolean = false,
    val statusMessage: String = "",
    val availableLanguages: List<Locale> = emptyList(),
    val selectedLanguage: Locale = Locale.getDefault(),
    val searchQuery: String = "",
    val isSearching: Boolean = false
)
