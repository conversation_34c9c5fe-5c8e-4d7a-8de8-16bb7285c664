package com.tfkcolin.practice.features.conversation.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.language.domain.LanguageManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ConversationMessage(
    val text: String,
    val isUser: Boolean,
    val hasAudio: Boolean = false,
    val isCorrection: Boolean = false
)

data class ConversationUiState(
    val messages: List<ConversationMessage> = emptyList(),
    val currentInput: String = "",
    val isListening: Boolean = false,
    val isEngineReady: Boolean = false,
    val statusMessage: String = "",
    val suggestedResponse: String = "Se dice \"madre\"."
)

@HiltViewModel
class ConversationViewModel @Inject constructor(
    private val languageManager: LanguageManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(ConversationUiState())
    val uiState: StateFlow<ConversationUiState> = _uiState

    private val conversationStarters = listOf(
        "¡Hola! Soy tu profesor de español. ¿Qué tema te gustaría aprender hoy?",
        "¡Excelente! Empecemos con las palabras. ¿Cómo se dice \"mother\" en español?",
        "¡Muy bien! Ahora, ¿puedes usar \"madre\" en una oración?"
    )

    init {
        // Check if engines are ready
        viewModelScope.launch {
            languageManager.state.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isEngineReady = state.isReady,
                    statusMessage = state.error ?: ""
                )
            }
        }
        
        // Initialize conversation
        initializeConversation()
    }

    private fun initializeConversation() {
        val initialMessages = listOf(
            ConversationMessage(
                text = conversationStarters[0],
                isUser = false,
                hasAudio = true
            )
        )
        _uiState.value = _uiState.value.copy(messages = initialMessages)
    }

    fun startListening() {
        if (_uiState.value.isEngineReady && !_uiState.value.isListening) {
            _uiState.value = _uiState.value.copy(isListening = true, currentInput = "")
            
            val speechRepository = languageManager.getCurrentSpeechRepository()
            speechRepository.startListening(
                callback = { text ->
                    _uiState.value = _uiState.value.copy(
                        currentInput = text,
                        isListening = speechRepository.isListening
                    )
                },
                errorCallback = { error ->
                    _uiState.value = _uiState.value.copy(
                        statusMessage = error,
                        isListening = false
                    )
                }
            )
        }
    }

    fun stopListening() {
        val speechRepository = languageManager.getCurrentSpeechRepository()
        speechRepository.stopListening()
        _uiState.value = _uiState.value.copy(isListening = false)
        
        // If we have input, add it as a user message
        if (_uiState.value.currentInput.isNotEmpty()) {
            sendMessage(_uiState.value.currentInput)
        }
    }

    fun sendMessage(text: String) {
        if (text.isBlank()) return
        
        // Add user message
        val userMessage = ConversationMessage(
            text = text,
            isUser = true,
            isCorrection = text.contains("madre") // Simple correction detection
        )
        
        val updatedMessages = _uiState.value.messages + userMessage
        _uiState.value = _uiState.value.copy(
            messages = updatedMessages,
            currentInput = ""
        )
        
        // Generate AI response (simplified)
        generateAIResponse(text)
    }

    private fun generateAIResponse(userInput: String) {
        viewModelScope.launch {
            // Simulate AI thinking time
            kotlinx.coroutines.delay(1000)
            
            val response = when {
                userInput.contains("familia") || userInput.contains("family") -> 
                    conversationStarters[1]
                userInput.contains("madre") || userInput.contains("mother") -> 
                    conversationStarters[2]
                else -> "¡Muy bien! Sigamos practicando. ¿Puedes decir algo más?"
            }
            
            val aiMessage = ConversationMessage(
                text = response,
                isUser = false,
                hasAudio = true
            )
            
            val updatedMessages = _uiState.value.messages + aiMessage
            _uiState.value = _uiState.value.copy(messages = updatedMessages)
        }
    }

    fun playAudio(text: String) {
        if (_uiState.value.isEngineReady) {
            val ttsRepository = languageManager.getCurrentTtsRepository()
            ttsRepository.speak(text)
        }
    }

    fun updateInput(text: String) {
        _uiState.value = _uiState.value.copy(currentInput = text)
    }

    fun clearInput() {
        _uiState.value = _uiState.value.copy(currentInput = "")
    }

    fun resetConversation() {
        _uiState.value = _uiState.value.copy(
            messages = emptyList(),
            currentInput = "",
            isListening = false
        )
        initializeConversation()
    }
}
