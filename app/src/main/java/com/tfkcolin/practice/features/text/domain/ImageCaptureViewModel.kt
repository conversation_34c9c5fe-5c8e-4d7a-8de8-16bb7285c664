package com.tfkcolin.practice.features.text.domain

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.features.text.data.ImageTextExtractionService
import com.tfkcolin.practice.features.text.data.LanguageDetectionService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ImageCaptureUiState(
    val isLoading: Boolean = false,
    val extractedText: String? = null,
    val detectedLanguage: String? = null,
    val error: String? = null,
    val selectedImageUri: Uri? = null,
    val capturedBitmap: Bitmap? = null
)

@HiltViewModel
class ImageCaptureViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val imageTextExtractionService: ImageTextExtractionService,
    private val languageDetectionService: LanguageDetectionService
) : ViewModel() {

    private val _uiState = MutableStateFlow(ImageCaptureUiState())
    val uiState: StateFlow<ImageCaptureUiState> = _uiState.asStateFlow()

    fun onImageSelected(uri: Uri) {
        _uiState.value = _uiState.value.copy(
            selectedImageUri = uri,
            error = null
        )
        extractTextFromUri(uri)
    }

    fun onImageCaptured(bitmap: Bitmap) {
        _uiState.value = _uiState.value.copy(
            capturedBitmap = bitmap,
            error = null
        )
        extractTextFromBitmap(bitmap)
    }

    private fun extractTextFromUri(uri: Uri) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                val result = imageTextExtractionService.extractTextFromUri(context, uri)
                result.fold(
                    onSuccess = { text ->
                        if (text.isNotBlank()) {
                            _uiState.value = _uiState.value.copy(
                                extractedText = text,
                                isLoading = false
                            )
                            detectLanguage(text)
                        } else {
                            _uiState.value = _uiState.value.copy(
                                error = "No text found in the image. Try a clearer image.",
                                isLoading = false
                            )
                        }
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to extract text: ${exception.localizedMessage}",
                            isLoading = false
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Unexpected error: ${e.localizedMessage}",
                    isLoading = false
                )
            }
        }
    }

    private fun extractTextFromBitmap(bitmap: Bitmap) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                val result = imageTextExtractionService.extractTextFromBitmap(bitmap)
                result.fold(
                    onSuccess = { text ->
                        if (text.isNotBlank()) {
                            _uiState.value = _uiState.value.copy(
                                extractedText = text,
                                isLoading = false
                            )
                            detectLanguage(text)
                        } else {
                            _uiState.value = _uiState.value.copy(
                                error = "No text found in the image. Try a clearer image.",
                                isLoading = false
                            )
                        }
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to extract text: ${exception.localizedMessage}",
                            isLoading = false
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Unexpected error: ${e.localizedMessage}",
                    isLoading = false
                )
            }
        }
    }

    private fun detectLanguage(text: String) {
        viewModelScope.launch {
            try {
                val result = languageDetectionService.detectLanguage(text)
                result.fold(
                    onSuccess = { language ->
                        _uiState.value = _uiState.value.copy(detectedLanguage = language)
                    },
                    onFailure = { exception ->
                        // Language detection failure is not critical, continue without it
                        _uiState.value = _uiState.value.copy(detectedLanguage = null)
                    }
                )
            } catch (e: Exception) {
                // Language detection failure is not critical
                _uiState.value = _uiState.value.copy(detectedLanguage = null)
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun reset() {
        _uiState.value = ImageCaptureUiState()
    }

    fun canProceedToProcessing(): Boolean {
        return _uiState.value.extractedText?.isNotBlank() == true
    }
}