package com.tfkcolin.practice.features.practice.domain

import com.tfkcolin.practice.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max
import kotlin.math.min

@Singleton
class PracticeAnalyticsService @Inject constructor(
    private val practiceSessionDao: PracticeSessionDao,
    private val practiceResultDao: PracticeResultDao,
    private val userPracticeProfileDao: UserPracticeProfileDao,
    private val itemMasteryDao: ItemMasteryDao,
    private val practiceStreakDao: PracticeStreakDao,
    private val practiceAchievementDao: PracticeAchievementDao
) {

    suspend fun generateSessionAnalytics(sessionId: String): LearningAnalytics {
        val session = practiceSessionDao.getSessionById(sessionId)
            ?: throw IllegalArgumentException("Session not found: $sessionId")
        
        val results = practiceResultDao.getResultsBySession(sessionId)
        
        val itemAnalytics = results.map { result ->
            ItemAnalytics(
                itemId = result.itemId,
                itemType = result.itemType,
                attempts = result.attempts,
                timeSpent = result.timeSpent,
                finalScore = calculateOverallScore(result),
                difficultyLevel = DifficultyLevel.INTERMEDIATE, // Would be determined from item data
                wasMarkedAsLearned = result.markedAsLearned
            )
        }
        
        val averageScore = itemAnalytics.map { it.finalScore }.average().toFloat()
        val completionRate = session.completedItems.toFloat() / session.totalItems
        
        val improvementAreas = identifyImprovementAreas(itemAnalytics)
        val achievements = checkForNewAchievements(session.userId, session, itemAnalytics)
        
        return LearningAnalytics(
            sessionId = sessionId,
            userId = session.userId,
            itemAnalytics = itemAnalytics,
            sessionDuration = session.sessionDuration,
            completionRate = completionRate,
            averageScore = averageScore,
            improvementAreas = improvementAreas,
            achievements = achievements
        )
    }
    
    suspend fun getUserProgressSummary(userId: String, targetLanguage: String): UserProgressSummary {
        val profile = userPracticeProfileDao.getProfile(userId, targetLanguage)
        val recentSessions = practiceSessionDao.getRecentSessions(userId, 10)
            .filter { it.targetLanguage == targetLanguage }
        
        val overallMastery = itemMasteryDao.getOverallMasteryLevel(userId) ?: 0f
        val currentStreak = calculateCurrentStreak(userId, targetLanguage)
        val weeklyProgress = calculateWeeklyProgress(userId, targetLanguage)
        
        return UserProgressSummary(
            userId = userId,
            targetLanguage = targetLanguage,
            totalSessions = profile?.totalSessions ?: 0,
            totalPracticeTime = profile?.totalPracticeTime ?: 0,
            averageScore = profile?.averageSessionScore ?: 0f,
            overallMastery = overallMastery,
            currentStreak = currentStreak,
            weeklyProgress = weeklyProgress,
            recentSessions = recentSessions.map { it.sessionId },
            strongAreas = profile?.strongAreas ?: emptyList(),
            weakAreas = profile?.weakAreas ?: emptyList()
        )
    }
    
    suspend fun getDetailedItemProgress(userId: String, itemId: String): ItemProgressDetail {
        val mastery = itemMasteryDao.getMastery(userId, itemId)
        val results = practiceResultDao.getResultsByItem(itemId)
        
        val progressHistory = results.map { result ->
            PracticeAttempt(
                timestamp = result.timestamp,
                overallScore = calculateOverallScore(result),
                listeningScore = result.listeningScore,
                pronunciationScore = result.pronunciationScore,
                comprehensionScore = result.comprehensionScore,
                timeSpent = result.timeSpent
            )
        }.sortedBy { it.timestamp }
        
        val improvementTrend = calculateImprovementTrend(progressHistory)
        val averageScore = progressHistory.map { it.overallScore }.average().toFloat()
        
        return ItemProgressDetail(
            itemId = itemId,
            userId = userId,
            currentMasteryLevel = mastery?.masteryLevel ?: 0f,
            practiceCount = mastery?.practiceCount ?: 0,
            averageScore = averageScore,
            improvementTrend = improvementTrend,
            progressHistory = progressHistory,
            lastPracticed = mastery?.lastPracticed,
            isMarkedAsLearned = mastery?.isMarkedAsLearned ?: false
        )
    }
    
    suspend fun updateUserStreak(userId: String, targetLanguage: String) {
        val today = System.currentTimeMillis()
        val startOfDay = getStartOfDay(today)
        
        // Check if user already practiced today
        val todayStreak = practiceStreakDao.getStreakForDate(userId, targetLanguage, startOfDay)
        
        if (todayStreak == null) {
            // Create new streak entry for today
            val streakEntity = PracticeStreakEntity(
                streakId = java.util.UUID.randomUUID().toString(),
                userId = userId,
                targetLanguage = targetLanguage,
                practiceDate = startOfDay,
                sessionsCompleted = 1,
                totalPracticeTime = 0, // Will be updated when session completes
                averageScore = 0f
            )
            practiceStreakDao.insertStreak(streakEntity)
            
            // Update user profile streak
            val currentStreak = calculateCurrentStreak(userId, targetLanguage)
            val profile = userPracticeProfileDao.getProfile(userId, targetLanguage)
            val longestStreak = max(currentStreak, profile?.longestStreak ?: 0)
            
            userPracticeProfileDao.updateStreak(
                userId = userId,
                targetLanguage = targetLanguage,
                streak = currentStreak,
                date = today,
                timestamp = today
            )
        }
    }
    
    private suspend fun calculateCurrentStreak(userId: String, targetLanguage: String): Int {
        val today = getStartOfDay(System.currentTimeMillis())
        val sevenDaysAgo = today - (7 * 24 * 60 * 60 * 1000)
        
        return practiceStreakDao.getCurrentStreak(userId, targetLanguage, sevenDaysAgo)
    }
    
    private suspend fun calculateWeeklyProgress(userId: String, targetLanguage: String): WeeklyProgress {
        val oneWeekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        val recentSessions = practiceSessionDao.getRecentSessions(userId, 50)
            .filter { it.targetLanguage == targetLanguage && it.createdAt >= oneWeekAgo }
        
        val totalSessions = recentSessions.size
        val totalTime = recentSessions.sumOf { it.sessionDuration }
        val averageScore = if (recentSessions.isNotEmpty()) {
            recentSessions.map { it.averageScore }.average().toFloat()
        } else 0f
        
        val dailyProgress = (0..6).map { dayOffset ->
            val dayStart = getStartOfDay(System.currentTimeMillis() - (dayOffset * 24 * 60 * 60 * 1000))
            val dayEnd = dayStart + (24 * 60 * 60 * 1000)
            
            val daySessions = recentSessions.filter { it.createdAt in dayStart until dayEnd }
            
            DailyProgress(
                date = dayStart,
                sessionsCompleted = daySessions.size,
                totalTime = daySessions.sumOf { it.sessionDuration },
                averageScore = if (daySessions.isNotEmpty()) {
                    daySessions.map { it.averageScore }.average().toFloat()
                } else 0f
            )
        }.reversed()
        
        return WeeklyProgress(
            totalSessions = totalSessions,
            totalTime = totalTime,
            averageScore = averageScore,
            dailyProgress = dailyProgress
        )
    }
    
    private fun calculateOverallScore(result: PracticeResultEntity): Float {
        val scores = listOfNotNull(
            result.listeningScore,
            result.pronunciationScore,
            result.comprehensionScore
        )
        return if (scores.isNotEmpty()) scores.average().toFloat() else 0f
    }
    
    private fun identifyImprovementAreas(itemAnalytics: List<ItemAnalytics>): List<String> {
        val areas = mutableListOf<String>()
        
        val pronunciationScores = itemAnalytics.mapNotNull { 
            // This would need access to individual scores, simplified for now
            if (it.finalScore < 0.7f) it.finalScore else null
        }
        
        if (pronunciationScores.isNotEmpty() && pronunciationScores.average() < 0.7) {
            areas.add("Pronunciation accuracy needs improvement")
        }
        
        val timeSpentAverage = itemAnalytics.map { it.timeSpent }.average()
        if (timeSpentAverage > 60000) { // More than 1 minute per item
            areas.add("Consider practicing more frequently for better retention")
        }
        
        val lowScoreItems = itemAnalytics.filter { it.finalScore < 0.6f }
        if (lowScoreItems.size > itemAnalytics.size * 0.3) {
            areas.add("Focus on comprehension and listening skills")
        }
        
        return areas
    }
    
    private suspend fun checkForNewAchievements(
        userId: String,
        session: PracticeSessionEntity,
        itemAnalytics: List<ItemAnalytics>
    ): List<String> {
        val achievements = mutableListOf<String>()
        
        // Check for first session achievement
        val totalSessions = practiceSessionDao.getCompletedSessionCount(userId, session.targetLanguage)
        if (totalSessions == 1) {
            unlockAchievement(userId, AchievementType.FIRST_SESSION, session.targetLanguage)
            achievements.add("First Practice Session Complete!")
        }
        
        // Check for perfect session
        if (session.averageScore >= 0.95f) {
            unlockAchievement(userId, AchievementType.PERFECT_SESSION, session.targetLanguage)
            achievements.add("Perfect Session!")
        }
        
        // Check for streak achievements
        val currentStreak = calculateCurrentStreak(userId, session.targetLanguage)
        when (currentStreak) {
            3 -> {
                unlockAchievement(userId, AchievementType.STREAK_3_DAYS, session.targetLanguage)
                achievements.add("3-Day Streak!")
            }
            7 -> {
                unlockAchievement(userId, AchievementType.STREAK_7_DAYS, session.targetLanguage)
                achievements.add("Week Streak!")
            }
            30 -> {
                unlockAchievement(userId, AchievementType.STREAK_30_DAYS, session.targetLanguage)
                achievements.add("Month Streak!")
            }
        }
        
        return achievements
    }
    
    private suspend fun unlockAchievement(
        userId: String,
        type: AchievementType,
        targetLanguage: String?
    ) {
        val existing = practiceAchievementDao.getAchievementByType(userId, type)
        if (existing == null) {
            val achievement = PracticeAchievementEntity(
                achievementId = java.util.UUID.randomUUID().toString(),
                userId = userId,
                achievementType = type,
                title = getAchievementTitle(type),
                description = getAchievementDescription(type),
                iconResource = getAchievementIcon(type),
                targetLanguage = targetLanguage
            )
            practiceAchievementDao.insertAchievement(achievement)
        }
    }
    
    private fun calculateImprovementTrend(progressHistory: List<PracticeAttempt>): Float {
        if (progressHistory.size < 2) return 0f
        
        val recent = progressHistory.takeLast(5).map { it.overallScore }
        val earlier = progressHistory.dropLast(5).takeLast(5).map { it.overallScore }
        
        if (earlier.isEmpty()) return 0f
        
        val recentAverage = recent.average().toFloat()
        val earlierAverage = earlier.average().toFloat()
        
        return recentAverage - earlierAverage
    }
    
    private fun getStartOfDay(timestamp: Long): Long {
        return timestamp - (timestamp % (24 * 60 * 60 * 1000))
    }
    
    private fun getAchievementTitle(type: AchievementType): String = when (type) {
        AchievementType.FIRST_SESSION -> "Getting Started"
        AchievementType.STREAK_3_DAYS -> "Consistent Learner"
        AchievementType.STREAK_7_DAYS -> "Week Warrior"
        AchievementType.STREAK_30_DAYS -> "Monthly Master"
        AchievementType.PERFECT_SESSION -> "Perfectionist"
        AchievementType.PRONUNCIATION_MASTER -> "Pronunciation Pro"
        AchievementType.VOCABULARY_CHAMPION -> "Word Wizard"
        AchievementType.LISTENING_EXPERT -> "Listening Legend"
        AchievementType.FAST_LEARNER -> "Quick Study"
        AchievementType.PERSISTENT_LEARNER -> "Never Give Up"
        AchievementType.MULTILINGUAL -> "Polyglot"
    }
    
    private fun getAchievementDescription(type: AchievementType): String = when (type) {
        AchievementType.FIRST_SESSION -> "Completed your first practice session"
        AchievementType.STREAK_3_DAYS -> "Practiced for 3 consecutive days"
        AchievementType.STREAK_7_DAYS -> "Maintained a 7-day practice streak"
        AchievementType.STREAK_30_DAYS -> "Incredible 30-day practice streak"
        AchievementType.PERFECT_SESSION -> "Achieved 95%+ average score in a session"
        AchievementType.PRONUNCIATION_MASTER -> "Excellent pronunciation scores"
        AchievementType.VOCABULARY_CHAMPION -> "Mastered vocabulary practice"
        AchievementType.LISTENING_EXPERT -> "Outstanding listening comprehension"
        AchievementType.FAST_LEARNER -> "Quick to master new content"
        AchievementType.PERSISTENT_LEARNER -> "Consistent practice over time"
        AchievementType.MULTILINGUAL -> "Practicing multiple languages"
    }
    
    private fun getAchievementIcon(type: AchievementType): String = when (type) {
        AchievementType.FIRST_SESSION -> "🎯"
        AchievementType.STREAK_3_DAYS -> "🔥"
        AchievementType.STREAK_7_DAYS -> "⭐"
        AchievementType.STREAK_30_DAYS -> "🏆"
        AchievementType.PERFECT_SESSION -> "💯"
        AchievementType.PRONUNCIATION_MASTER -> "🎤"
        AchievementType.VOCABULARY_CHAMPION -> "📚"
        AchievementType.LISTENING_EXPERT -> "👂"
        AchievementType.FAST_LEARNER -> "⚡"
        AchievementType.PERSISTENT_LEARNER -> "💪"
        AchievementType.MULTILINGUAL -> "🌍"
    }
}

// Additional data classes for analytics
data class UserProgressSummary(
    val userId: String,
    val targetLanguage: String,
    val totalSessions: Int,
    val totalPracticeTime: Long,
    val averageScore: Float,
    val overallMastery: Float,
    val currentStreak: Int,
    val weeklyProgress: WeeklyProgress,
    val recentSessions: List<String>,
    val strongAreas: List<String>,
    val weakAreas: List<String>
)

data class WeeklyProgress(
    val totalSessions: Int,
    val totalTime: Long,
    val averageScore: Float,
    val dailyProgress: List<DailyProgress>
)

data class DailyProgress(
    val date: Long,
    val sessionsCompleted: Int,
    val totalTime: Long,
    val averageScore: Float
)

data class ItemProgressDetail(
    val itemId: String,
    val userId: String,
    val currentMasteryLevel: Float,
    val practiceCount: Int,
    val averageScore: Float,
    val improvementTrend: Float,
    val progressHistory: List<PracticeAttempt>,
    val lastPracticed: Long?,
    val isMarkedAsLearned: Boolean
)

data class PracticeAttempt(
    val timestamp: Long,
    val overallScore: Float,
    val listeningScore: Float?,
    val pronunciationScore: Float?,
    val comprehensionScore: Float?,
    val timeSpent: Long
)
