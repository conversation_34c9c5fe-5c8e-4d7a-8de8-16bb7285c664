package com.tfkcolin.practice.features.text.data

import com.google.mlkit.nl.languageid.LanguageIdentification
import com.google.mlkit.nl.languageid.LanguageIdentifier
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Result of language detection with confidence score
 */
data class LanguageDetectionResult(
    val languageCode: String,
    val confidence: Float
)

interface LanguageDetectionService {
    suspend fun detectLanguage(text: String): Result<String>
    suspend fun detectLanguageWithConfidence(text: String): Result<LanguageDetectionResult>
}

@Singleton
class MLKitLanguageDetectionService @Inject constructor() : LanguageDetectionService {

    private val languageIdentifier: LanguageIdentifier by lazy {
        LanguageIdentification.getClient()
    }

    override suspend fun detectLanguage(text: String): Result<String> {
        return try {
            val languageCode = languageIdentifier.identifyLanguage(text).await()
            val detectedLanguage = if (languageCode == "und") "auto" else languageCode
            Result.success(detectedLanguage)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun detectLanguageWithConfidence(text: String): Result<LanguageDetectionResult> {
        return try {
            // Get the primary language detection
            val languageCode = languageIdentifier.identifyLanguage(text).await()

            // Get possible languages with confidence scores
            val possibleLanguages = languageIdentifier.identifyPossibleLanguages(text).await()
            val confidence = possibleLanguages.firstOrNull()?.confidence ?: 0f

            // Normalize the language code
            val normalizedCode = if (languageCode == "und" || confidence < 0.5f) {
                "auto"
            } else {
                languageCode
            }

            Result.success(LanguageDetectionResult(normalizedCode, confidence))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}