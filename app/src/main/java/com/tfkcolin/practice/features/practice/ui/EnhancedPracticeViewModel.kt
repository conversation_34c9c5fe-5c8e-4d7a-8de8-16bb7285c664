package com.tfkcolin.practice.features.practice.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.model.*
import com.tfkcolin.practice.features.practice.domain.PracticeContentSelector
import com.tfkcolin.practice.features.practice.domain.PronunciationAnalyzer
import com.tfkcolin.practice.features.speech.repository.SpeechRepository
import com.tfkcolin.practice.features.tts.repository.TtsRepository
import com.tfkcolin.practice.features.auth.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.*
import javax.inject.Inject

@HiltViewModel
class EnhancedPracticeViewModel @Inject constructor(
    private val contentSelector: PracticeContentSelector,
    private val pronunciationAnalyzer: PronunciationAnalyzer,
    private val speechRepository: SpeechRepository,
    private val ttsRepository: TtsRepository,
    private val authRepository: AuthRepository,
    private val practiceSessionDao: PracticeSessionDao,
    private val practiceResultDao: PracticeResultDao,
    private val itemMasteryDao: ItemMasteryDao,
    private val userPracticeProfileDao: UserPracticeProfileDao
) : ViewModel() {

    private val _uiState = MutableStateFlow(
        PracticeFlowState(
            currentSession = PracticeSession(
                targetLanguage = "",
                primaryTranslation = Translation(
                    id = "", originalText = "", translatedText = "", 
                    sourceLanguage = "", targetLanguage = "", timestamp = "",
                    source = "", isStarred = false, words = emptyList(), expressions = emptyList()
                ),
                practiceItems = emptyList()
            )
        )
    )
    val uiState: StateFlow<PracticeFlowState> = _uiState.asStateFlow()

    private var currentItemStartTime: Long = 0
    private var sessionStartTime: Long = 0
    private val currentUserId: String
        get() = authRepository.getCurrentUser()?.uid ?: ""

    fun initializePracticeSession(targetLanguage: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val session = contentSelector.generatePracticeSession(
                    userId = currentUserId,
                    targetLanguage = targetLanguage
                )
                
                // Save session to database
                val sessionEntity = PracticeSessionEntity(
                    sessionId = session.sessionId,
                    userId = currentUserId,
                    targetLanguage = targetLanguage,
                    primaryTranslationId = session.primaryTranslation.id,
                    supplementaryTranslationIds = session.supplementaryTranslations.map { it.id },
                    sessionType = session.sessionType,
                    totalItems = session.practiceItems.size,
                    completedItems = 0,
                    averageScore = 0f,
                    sessionDuration = 0,
                    createdAt = System.currentTimeMillis()
                )
                practiceSessionDao.insertSession(sessionEntity)
                
                sessionStartTime = System.currentTimeMillis()
                currentItemStartTime = sessionStartTime
                
                _uiState.value = PracticeFlowState(
                    currentSession = session,
                    currentItemIndex = 0,
                    practiceMode = PracticeMode.LISTENING,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to initialize practice session: ${e.message}"
                )
            }
        }
    }

    fun playItemAudio() {
        val currentItem = getCurrentItem() ?: return
        
        viewModelScope.launch {
            try {
                ttsRepository.speak(currentItem.content)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to play audio: ${e.message}"
                )
            }
        }
    }

    fun moveToSpeakingMode() {
        _uiState.value = _uiState.value.copy(
            practiceMode = PracticeMode.SPEAKING,
            userResponse = "",
            pronunciationScore = null,
            transcriptionResult = null,
            showFeedback = false
        )
    }

    fun startRecording() {
        val currentItem = getCurrentItem() ?: return
        
        viewModelScope.launch {
            try {
                speechRepository.startListening(
                    callback = { transcription ->
                        handleTranscriptionResult(transcription)
                    },
                    errorCallback = { error ->
                        _uiState.value = _uiState.value.copy(
                            error = "Speech recognition error: $error"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to start recording: ${e.message}"
                )
            }
        }
    }

    fun stopRecording() {
        viewModelScope.launch {
            try {
                speechRepository.stopListening()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to stop recording: ${e.message}"
                )
            }
        }
    }

    private fun handleTranscriptionResult(transcription: String) {
        val currentItem = getCurrentItem() ?: return
        
        viewModelScope.launch {
            try {
                val analysis = pronunciationAnalyzer.analyzePronunciation(
                    targetText = currentItem.content,
                    userTranscription = transcription,
                    targetLanguage = _uiState.value.currentSession.targetLanguage
                )
                
                _uiState.value = _uiState.value.copy(
                    userResponse = transcription,
                    pronunciationScore = analysis.overallScore,
                    transcriptionResult = transcription,
                    showFeedback = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to analyze pronunciation: ${e.message}"
                )
            }
        }
    }

    fun retryPronunciation() {
        _uiState.value = _uiState.value.copy(
            userResponse = "",
            pronunciationScore = null,
            transcriptionResult = null,
            showFeedback = false
        )
    }

    fun moveToComprehensionMode() {
        _uiState.value = _uiState.value.copy(
            practiceMode = PracticeMode.COMPREHENSION
        )
    }

    fun selectComprehensionAnswer(selectedTranslation: String) {
        val currentItem = getCurrentItem() ?: return
        val isCorrect = currentItem.translations.contains(selectedTranslation)
        
        // For now, just move to completion. In a full implementation,
        // you'd track the comprehension score based on correctness
        _uiState.value = _uiState.value.copy(
            // Could add comprehension score tracking here
        )
    }

    fun completeItemPractice() {
        val currentItem = getCurrentItem() ?: return
        val currentState = _uiState.value
        
        viewModelScope.launch {
            try {
                // Calculate time spent on this item
                val timeSpent = System.currentTimeMillis() - currentItemStartTime
                
                // Create practice result
                val result = PracticeResult(
                    item = currentItem,
                    listeningScore = 1.0f, // Assume listening was successful if they proceeded
                    pronunciationScore = currentState.pronunciationScore,
                    comprehensionScore = 1.0f, // Simplified for now
                    attempts = 1, // Track attempts in a full implementation
                    timeSpent = timeSpent,
                    markedAsLearned = false
                )
                
                // Save result to database
                val resultEntity = PracticeResultEntity(
                    resultId = UUID.randomUUID().toString(),
                    sessionId = currentState.currentSession.sessionId,
                    itemId = currentItem.id,
                    itemType = currentItem.itemType,
                    listeningScore = result.listeningScore,
                    pronunciationScore = result.pronunciationScore,
                    comprehensionScore = result.comprehensionScore,
                    attempts = result.attempts,
                    timeSpent = result.timeSpent,
                    markedAsLearned = result.markedAsLearned
                )
                practiceResultDao.insertResult(resultEntity)
                
                // Update item mastery
                updateItemMastery(currentItem, result)
                
                // Add to completed items
                val updatedCompletedItems = currentState.completedItems + result
                val progress = updatedCompletedItems.size.toFloat() / currentState.currentSession.practiceItems.size
                
                // Check if session is complete
                if (currentState.currentItemIndex >= currentState.currentSession.practiceItems.size - 1) {
                    completeSession(updatedCompletedItems)
                } else {
                    // Move to next item
                    moveToNextItem(updatedCompletedItems, progress)
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to complete item practice: ${e.message}"
                )
            }
        }
    }

    private suspend fun updateItemMastery(item: PracticeItem, result: PracticeResult) {
        val overallScore = listOfNotNull(
            result.listeningScore,
            result.pronunciationScore,
            result.comprehensionScore
        ).average().toFloat()
        
        // Update or create mastery record
        val existingMastery = itemMasteryDao.getMastery(currentUserId, item.id)
        if (existingMastery != null) {
            // Update existing mastery with weighted average
            val newMasteryLevel = (existingMastery.masteryLevel * 0.7f + overallScore * 0.3f)
                .coerceIn(0f, 1f)
            
            itemMasteryDao.updateMasteryLevel(
                userId = currentUserId,
                itemId = item.id,
                level = newMasteryLevel,
                score = overallScore,
                timestamp = System.currentTimeMillis()
            )
        } else {
            // Create new mastery record
            val masteryEntity = ItemMasteryEntity(
                masteryId = UUID.randomUUID().toString(),
                userId = currentUserId,
                itemId = item.id,
                itemContent = item.content,
                itemType = item.itemType,
                masteryLevel = overallScore,
                practiceCount = 1,
                successfulAttempts = if (overallScore >= 0.7f) 1 else 0,
                lastScore = overallScore,
                lastPracticed = System.currentTimeMillis()
            )
            itemMasteryDao.insertMastery(masteryEntity)
        }
    }

    private fun moveToNextItem(completedItems: List<PracticeResult>, progress: Float) {
        currentItemStartTime = System.currentTimeMillis()
        
        _uiState.value = _uiState.value.copy(
            currentItemIndex = _uiState.value.currentItemIndex + 1,
            practiceMode = PracticeMode.LISTENING,
            userResponse = "",
            pronunciationScore = null,
            transcriptionResult = null,
            showFeedback = false,
            completedItems = completedItems,
            sessionProgress = progress
        )
    }

    private suspend fun completeSession(completedItems: List<PracticeResult>) {
        val sessionDuration = System.currentTimeMillis() - sessionStartTime
        val averageScore = completedItems.mapNotNull { result ->
            listOfNotNull(
                result.listeningScore,
                result.pronunciationScore,
                result.comprehensionScore
            ).takeIf { it.isNotEmpty() }?.average()?.toFloat()
        }.average().toFloat()
        
        // Update session in database
        val currentSession = _uiState.value.currentSession
        val updatedSessionEntity = PracticeSessionEntity(
            sessionId = currentSession.sessionId,
            userId = currentUserId,
            targetLanguage = currentSession.targetLanguage,
            primaryTranslationId = currentSession.primaryTranslation.id,
            supplementaryTranslationIds = currentSession.supplementaryTranslations.map { it.id },
            sessionType = currentSession.sessionType,
            totalItems = currentSession.practiceItems.size,
            completedItems = completedItems.size,
            averageScore = averageScore,
            sessionDuration = sessionDuration,
            createdAt = sessionStartTime,
            completedAt = System.currentTimeMillis(),
            isCompleted = true
        )
        practiceSessionDao.updateSession(updatedSessionEntity)
        
        // Update user practice profile
        updateUserPracticeProfile(sessionDuration, averageScore)
        
        _uiState.value = _uiState.value.copy(
            completedItems = completedItems,
            sessionProgress = 1.0f,
            isSessionComplete = true
        )
    }

    private suspend fun updateUserPracticeProfile(sessionDuration: Long, averageScore: Float) {
        val targetLanguage = _uiState.value.currentSession.targetLanguage
        val existingProfile = userPracticeProfileDao.getProfile(currentUserId, targetLanguage)
        
        if (existingProfile != null) {
            val newTotalSessions = existingProfile.totalSessions + 1
            val newTotalTime = existingProfile.totalPracticeTime + sessionDuration
            val newAverageScore = (existingProfile.averageSessionScore * existingProfile.totalSessions + averageScore) / newTotalSessions
            
            userPracticeProfileDao.updateSessionStats(
                userId = currentUserId,
                targetLanguage = targetLanguage,
                sessionTime = sessionDuration,
                newAverage = newAverageScore,
                timestamp = System.currentTimeMillis()
            )
        } else {
            // Create new profile
            val profileEntity = UserPracticeProfileEntity(
                profileId = UUID.randomUUID().toString(),
                userId = currentUserId,
                targetLanguage = targetLanguage,
                totalSessions = 1,
                totalPracticeTime = sessionDuration,
                averageSessionScore = averageScore,
                lastPracticeDate = System.currentTimeMillis()
            )
            userPracticeProfileDao.insertProfile(profileEntity)
        }
    }

    fun markItemAsLearned(itemId: String) {
        viewModelScope.launch {
            try {
                itemMasteryDao.updateMasteryLevel(
                    userId = currentUserId,
                    itemId = itemId,
                    level = 1.0f, // Mark as fully learned
                    score = 1.0f,
                    timestamp = System.currentTimeMillis()
                )
                
                // Update the current item in the UI state
                val updatedItems = _uiState.value.currentSession.practiceItems.map { item ->
                    if (item.id == itemId) {
                        item.copy(isMarkedAsLearned = true, masteryScore = 1.0f)
                    } else {
                        item
                    }
                }
                
                val updatedSession = _uiState.value.currentSession.copy(practiceItems = updatedItems)
                _uiState.value = _uiState.value.copy(currentSession = updatedSession)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to mark item as learned: ${e.message}"
                )
            }
        }
    }

    fun skipCurrentItem() {
        val currentState = _uiState.value
        if (currentState.currentItemIndex >= currentState.currentSession.practiceItems.size - 1) {
            // Last item, complete session
            viewModelScope.launch {
                completeSession(currentState.completedItems)
            }
        } else {
            // Move to next item
            moveToNextItem(currentState.completedItems, currentState.sessionProgress)
        }
    }

    fun startFinalAssessment() {
        _uiState.value = _uiState.value.copy(
            finalAssessmentMode = true
        )
    }

    fun completeSession(assessmentResult: AssessmentResult) {
        // Handle final assessment completion
        viewModelScope.launch {
            try {
                // Save assessment results to database if needed
                // For now, just mark the session as fully complete
                _uiState.value = _uiState.value.copy(
                    finalAssessmentMode = false,
                    isSessionComplete = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to complete session: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun getCurrentItem(): PracticeItem? {
        val state = _uiState.value
        return if (state.currentItemIndex < state.currentSession.practiceItems.size) {
            state.currentSession.practiceItems[state.currentItemIndex]
        } else {
            null
        }
    }
}
