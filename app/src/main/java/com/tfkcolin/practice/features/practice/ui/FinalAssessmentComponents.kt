package com.tfkcolin.practice.features.practice.ui

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.practice.data.model.*
import com.tfkcolin.practice.ui.theme.*

@Composable
fun FinalAssessmentScreen(
    session: PracticeSession,
    results: List<PracticeResult>,
    onStartFinalAssessment: () -> Unit,
    onClose: () -> Unit
) {
    val averageScore = results.mapNotNull { result ->
        listOfNotNull(
            result.listeningScore,
            result.pronunciationScore,
            result.comprehensionScore
        ).takeIf { it.isNotEmpty() }?.average()?.toFloat()
    }.average().toFloat()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Session completion header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8)),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = Color(0xFF28A745)
                )
                Text(
                    text = "Practice Session Complete!",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = PrimaryBlack,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "You practiced ${results.size} items",
                    style = MaterialTheme.typography.bodyLarge,
                    color = PrimaryBlack.copy(alpha = 0.7f)
                )
            }
        }
        
        // Session statistics
        SessionStatistics(
            averageScore = averageScore,
            totalItems = results.size,
            learnedItems = results.count { it.markedAsLearned }
        )
        
        // Results summary
        Text(
            text = "Session Results",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.SemiBold,
            color = PrimaryBlack
        )
        
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(results) { result ->
                ResultItem(result = result)
            }
        }
        
        // Final assessment prompt
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFF3E0))
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "Ready for the final challenge?",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = PrimaryBlack
                )
                Text(
                    text = "Test your understanding by reading and listening to the complete original text(s).",
                    style = MaterialTheme.typography.bodyMedium,
                    color = PrimaryBlack.copy(alpha = 0.7f)
                )
            }
        }
        
        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedButton(
                onClick = onClose,
                modifier = Modifier.weight(1f)
            ) {
                Text("Finish Session")
            }
            
            Button(
                onClick = onStartFinalAssessment,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(containerColor = PrimaryBlue)
            ) {
                Text("Final Assessment")
            }
        }
    }
}

@Composable
fun SessionStatistics(
    averageScore: Float,
    totalItems: Int,
    learnedItems: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        StatCard(
            title = "Average Score",
            value = "${(averageScore * 100).toInt()}%",
            icon = Icons.Default.TrendingUp,
            color = when {
                averageScore >= 0.8f -> Color(0xFF28A745)
                averageScore >= 0.6f -> Color(0xFFFFC107)
                else -> Color(0xFFDC3545)
            },
            modifier = Modifier.weight(1f)
        )
        
        StatCard(
            title = "Items Learned",
            value = "$learnedItems/$totalItems",
            icon = Icons.Default.School,
            color = PrimaryBlue,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun StatCard(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = color
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = PrimaryBlack.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun ResultItem(result: PracticeResult) {
    val overallScore = listOfNotNull(
        result.listeningScore,
        result.pronunciationScore,
        result.comprehensionScore
    ).average().toFloat()
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Item content
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = result.item.content,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = PrimaryBlack
                )
                Text(
                    text = result.item.itemType.name.lowercase(),
                    style = MaterialTheme.typography.bodySmall,
                    color = PrimaryBlack.copy(alpha = 0.6f)
                )
            }
            
            // Score indicators
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                result.listeningScore?.let { score ->
                    ScoreIndicator(
                        score = score,
                        icon = Icons.Default.VolumeUp,
                        label = "Listen"
                    )
                }
                
                result.pronunciationScore?.let { score ->
                    ScoreIndicator(
                        score = score,
                        icon = Icons.Default.Mic,
                        label = "Speak"
                    )
                }
                
                result.comprehensionScore?.let { score ->
                    ScoreIndicator(
                        score = score,
                        icon = Icons.Default.Quiz,
                        label = "Understand"
                    )
                }
            }
            
            // Learned indicator
            if (result.markedAsLearned) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Learned",
                    tint = Color(0xFF28A745),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun ScoreIndicator(
    score: Float,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String
) {
    val color = when {
        score >= 0.8f -> Color(0xFF28A745)
        score >= 0.6f -> Color(0xFFFFC107)
        else -> Color(0xFFDC3545)
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        Text(
            text = "${(score * 100).toInt()}%",
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun FullTextAssessmentScreen(
    session: PracticeSession,
    onComplete: (AssessmentResult) -> Unit,
    onClose: () -> Unit
) {
    var assessmentMode by remember { mutableStateOf(AssessmentMode.READING) }
    var readingScore by remember { mutableStateOf(0f) }
    var listeningScore by remember { mutableStateOf(0f) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Text(
            text = "Final Assessment",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = PrimaryBlack
        )

        Text(
            text = "Test your understanding of the complete text(s) you practiced with.",
            style = MaterialTheme.typography.bodyLarge,
            color = PrimaryBlack.copy(alpha = 0.7f)
        )

        // Assessment mode tabs
        AssessmentModeSelector(
            currentMode = assessmentMode,
            onModeChange = { assessmentMode = it }
        )

        // Assessment content
        Box(
            modifier = Modifier.weight(1f)
        ) {
            when (assessmentMode) {
                AssessmentMode.READING -> {
                    FullTextReadingAssessment(
                        originalTexts = getOriginalTexts(session),
                        onScoreUpdate = { readingScore = it }
                    )
                }

                AssessmentMode.LISTENING -> {
                    FullTextListeningAssessment(
                        originalTexts = getOriginalTexts(session),
                        onScoreUpdate = { listeningScore = it }
                    )
                }
            }
        }

        // Complete assessment button
        AnimatedVisibility(
            visible = readingScore > 0 && listeningScore > 0,
            enter = slideInVertically() + fadeIn()
        ) {
            Button(
                onClick = {
                    onComplete(
                        AssessmentResult(
                            sessionId = session.sessionId,
                            readingScore = readingScore,
                            listeningScore = listeningScore,
                            overallScore = (readingScore + listeningScore) / 2
                        )
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(containerColor = PrimaryBlue),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "Complete Assessment",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }

        // Skip button
        if (readingScore == 0f || listeningScore == 0f) {
            OutlinedButton(
                onClick = onClose,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Skip Assessment")
            }
        }
    }
}

@Composable
fun AssessmentModeSelector(
    currentMode: AssessmentMode,
    onModeChange: (AssessmentMode) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        AssessmentMode.values().forEach { mode ->
            val isSelected = currentMode == mode

            FilterChip(
                onClick = { onModeChange(mode) },
                label = {
                    Text(
                        text = when (mode) {
                            AssessmentMode.READING -> "Reading"
                            AssessmentMode.LISTENING -> "Listening"
                        }
                    )
                },
                selected = isSelected,
                leadingIcon = {
                    Icon(
                        imageVector = when (mode) {
                            AssessmentMode.READING -> Icons.Default.MenuBook
                            AssessmentMode.LISTENING -> Icons.Default.VolumeUp
                        },
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
fun FullTextReadingAssessment(
    originalTexts: List<String>,
    onScoreUpdate: (Float) -> Unit
) {
    var hasRead by remember { mutableStateOf(false) }
    var comprehensionScore by remember { mutableStateOf(0f) }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Read the complete text(s) aloud:",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack
            )
        }

        items(originalTexts) { text ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
            ) {
                Text(
                    text = text,
                    style = MaterialTheme.typography.bodyLarge,
                    color = PrimaryBlack,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        item {
            if (!hasRead) {
                Button(
                    onClick = {
                        hasRead = true
                        // In a real implementation, this would start speech recognition
                        // For now, we'll simulate a score
                        comprehensionScore = 0.85f
                        onScoreUpdate(comprehensionScore)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF28A745))
                ) {
                    Icon(
                        imageVector = Icons.Default.Mic,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Start Reading Aloud")
                }
            } else {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = null,
                                tint = Color(0xFF28A745)
                            )
                            Text(
                                text = "Reading Assessment Complete",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = Color(0xFF155724)
                            )
                        }
                        Text(
                            text = "Reading Score: ${(comprehensionScore * 100).toInt()}%",
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF155724)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun FullTextListeningAssessment(
    originalTexts: List<String>,
    onScoreUpdate: (Float) -> Unit
) {
    var hasListened by remember { mutableStateOf(false) }
    var comprehensionScore by remember { mutableStateOf(0f) }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Listen to the complete text(s):",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = PrimaryBlack
        )

        if (!hasListened) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFE3F2FD))
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.VolumeUp,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = PrimaryBlue
                    )
                    Text(
                        text = "Listen carefully to the complete text",
                        style = MaterialTheme.typography.bodyLarge,
                        color = PrimaryBlack,
                        textAlign = TextAlign.Center
                    )
                    Button(
                        onClick = {
                            hasListened = true
                            // In a real implementation, this would play the TTS audio
                            // For now, we'll simulate listening completion
                            comprehensionScore = 0.90f
                            onScoreUpdate(comprehensionScore)
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = PrimaryBlue)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Play Audio")
                    }
                }
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = Color(0xFF28A745)
                        )
                        Text(
                            text = "Listening Assessment Complete",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF155724)
                        )
                    )
                    Text(
                        text = "Listening Score: ${(comprehensionScore * 100).toInt()}%",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color(0xFF155724)
                    )
                }
            }
        }
    }
}

private fun getOriginalTexts(session: PracticeSession): List<String> {
    val texts = mutableListOf<String>()
    texts.add(session.primaryTranslation.originalText)
    texts.addAll(session.supplementaryTranslations.map { it.originalText })
    return texts.distinct()
}
