package com.tfkcolin.practice.features.audio.domain

import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.features.audio.data.AudioProcessingProgress
import java.io.File

data class AudioPracticeUiState(
    // File selection state
    val selectedFile: File? = null,
    val isFileValid: Boolean = false,
    val fileError: String? = null,

    // Transcription state
    val transcription: String = "",
    val isTranscribing: Boolean = false,
    val transcriptionError: String? = null,
    val showProceedButton: Boolean = false,

    // Processing progress state
    val processingProgress: AudioProcessingProgress? = null,

    // AI Processing state (replaces practice state)
    val isProcessingText: Boolean = false,
    val processingError: String? = null,
    val isProcessingComplete: Boolean = false,
    val processedTranslation: Translation? = null
)