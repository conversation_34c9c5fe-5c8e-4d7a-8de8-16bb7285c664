package com.tfkcolin.practice.features.text.data

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.tasks.await

interface ImageTextExtractionService {

    /**
     * Extract text from a bitmap image
     * @param bitmap The bitmap to process
     * @return Result containing extracted text or error
     */
    suspend fun extractTextFromBitmap(bitmap: Bitmap): Result<String>

    /**
     * Extract text from a URI (gallery image)
     * @param context Android context
     * @param uri The URI of the image
     * @return Result containing extracted text or error
     */
    suspend fun extractTextFromUri(context: Context, uri: Uri): Result<String>

    /**
     * Check if the service is available
     */
    fun isAvailable(): Boolean
}

class MLKitImageTextExtractionService : ImageTextExtractionService {

    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

    override suspend fun extractTextFromBitmap(bitmap: Bitmap): Result<String> {
        return try {
            val image = InputImage.fromBitmap(bitmap, 0)
            val result = textRecognizer.process(image).await()
            val extractedText = result.text.trim()
            if (extractedText.isNotEmpty()) {
                Result.success(extractedText)
            } else {
                Result.failure(Exception("No text found in image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun extractTextFromUri(context: Context, uri: Uri): Result<String> {
        return try {
            val image = InputImage.fromFilePath(context, uri)
            val result = textRecognizer.process(image).await()
            val extractedText = result.text.trim()
            if (extractedText.isNotEmpty()) {
                Result.success(extractedText)
            } else {
                Result.failure(Exception("No text found in image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun isAvailable(): Boolean {
        return true // ML Kit text recognition is always available on supported devices
    }
}