package com.tfkcolin.practice.features.settings.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.settings.domain.SettingsViewModel
import com.tfkcolin.practice.features.text.data.LanguageValidator
import com.tfkcolin.practice.ui.components.LanguageSelectionItem
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit = {},
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
    ) {
        // Top bar with back button
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackPressed) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = PrimaryBlack
                )
            }
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = "Settings",
                fontSize = 20.sp,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack
            )
        }
        LazyColumn(
            contentPadding = PaddingValues(bottom = 32.dp)
        ) {
            // Speech & Voice Language Section
            item {
                Row(
                    modifier = Modifier.padding(horizontal = 24.dp, vertical = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.VolumeUp,
                        contentDescription = null,
                        tint = AccentBlue,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Speech & Voice Language",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = PrimaryBlack
                    )
                }

                Text(
                    text = "Controls what language the app speaks and understands when you talk",
                    fontSize = 14.sp,
                    color = DarkGray,
                    modifier = Modifier.padding(horizontal = 24.dp)
                )

                // Info card for speech & voice language
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = LightGray.copy(alpha = 0.3f)
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Info,
                            contentDescription = null,
                            tint = AccentBlue,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "This affects:\n• What language AI voices speak in\n• What language speech recognition understands\n• Pronunciation practice and conversation features",
                            fontSize = 12.sp,
                            color = PrimaryBlack,
                            lineHeight = 16.sp
                        )
                    }
                }
            }

            // Language list for Speech & Voice
            if (uiState.isLoading) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(
                                color = AccentBlue
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Loading languages...",
                                fontSize = 16.sp,
                                color = DarkGray,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else if (uiState.error != null) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Error loading languages",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = ErrorRed
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = uiState.error!!,
                            fontSize = 14.sp,
                            color = DarkGray,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Row {
                            Button(
                                onClick = { viewModel.retryInitialization() }
                            ) {
                                Text("Retry")
                            }
                            Spacer(modifier = Modifier.width(8.dp))
                            OutlinedButton(
                                onClick = { viewModel.clearError() }
                            ) {
                                Text("Dismiss")
                            }
                        }
                    }
                }
            } else {
                items(uiState.availableLanguages) { language ->
                    LanguageSelectionItem(
                        language = language.displayName,
                        isSelected = language == uiState.selectedLanguage,
                        isComingSoon = false,
                        onClick = {
                            viewModel.selectLanguage(language)
                        }
                    )
                }
                if (uiState.availableLanguages.isEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "No supported languages found",
                                fontSize = 16.sp,
                                color = DarkGray,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }

            item {
                HorizontalDivider(modifier = Modifier.padding(vertical = 16.dp))
            }

            // Translation Target Language Section
            item {
                Row(
                    modifier = Modifier.padding(horizontal = 24.dp, vertical = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.Translate,
                        contentDescription = null,
                        tint = AccentBlue,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Translation Language",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = PrimaryBlack
                    )
                }

                Text(
                    text = "Sets the language that imported text will be translated into",
                    fontSize = 14.sp,
                    color = DarkGray,
                    modifier = Modifier.padding(horizontal = 24.dp)
                )

                // Info card for translation language
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = LightGray.copy(alpha = 0.3f)
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Info,
                            contentDescription = null,
                            tint = AccentBlue,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "This affects:\n• Translations of imported documents\n• Text extracted from images\n• Audio transcriptions that get translated\n• Practice content in your preferred language",
                            fontSize = 12.sp,
                            color = PrimaryBlack,
                            lineHeight = 16.sp
                        )
                    }
                }
            }

            // Target language selection
            items(LanguageValidator.getSupportedTargetLanguages().toList()) { languageCode ->
                val displayName = LanguageValidator.getDisplayName(languageCode)
                LanguageSelectionItem(
                    language = displayName,
                    isSelected = languageCode == uiState.selectedTargetLanguage,
                    isComingSoon = false,
                    onClick = {
                        viewModel.selectTargetLanguage(languageCode)
                    }
                )
            }
        }
    }
}