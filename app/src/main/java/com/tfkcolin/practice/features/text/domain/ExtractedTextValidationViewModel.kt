package com.tfkcolin.practice.features.text.domain

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

data class ExtractedTextValidationUiState(
    val text: String = "",
    val isLoading: Boolean = false
)

@HiltViewModel
class ExtractedTextValidationViewModel @Inject constructor() : ViewModel() {

    private val _uiState = MutableStateFlow(ExtractedTextValidationUiState())
    val uiState: StateFlow<ExtractedTextValidationUiState> = _uiState.asStateFlow()

    fun initializeText(text: String) {
        _uiState.value = ExtractedTextValidationUiState(text = text)
    }

    fun updateText(newText: String) {
        _uiState.value = _uiState.value.copy(text = newText)
    }

    fun getValidatedText(): String {
        return _uiState.value.text.trim()
    }
}