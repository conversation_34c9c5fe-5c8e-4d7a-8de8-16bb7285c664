package com.tfkcolin.practice.features.text.data

import com.google.firebase.FirebaseApp
import com.google.firebase.ai.FirebaseAI
import com.google.firebase.ai.GenerativeModel
import com.google.firebase.ai.type.GenerationConfig
import com.google.firebase.ai.type.ThinkingConfig
import com.tfkcolin.practice.data.model.ExpressionExample
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.model.WordDetail
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.math.pow

interface AITextProcessingService {

    /**
     * Process text to extract translation data structure
     * @param text The text to process
     * @param sourceLanguage Detected source language
     * @param targetLanguage Target language for translation
     * @param callback Callback with translation result or error
     */
    fun processTextForTranslation(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        callback: (Result<Translation>) -> Unit
    )

    /**
     * Check if the service is available
     */
    fun isAvailable(): Boolean
}

class FirebaseAITextProcessingService : AITextProcessingService {

    companion object {
        private const val MAX_RETRIES = 3
        private const val INITIAL_RETRY_DELAY_MS = 1000L
        private const val TIMEOUT_MS = 60000L // 60 seconds timeout
    }

    private val generativeModel: GenerativeModel by lazy {
        FirebaseAI.getInstance(FirebaseApp.getInstance()).generativeModel(
            "gemini-2.5-flash",
            generationConfig = GenerationConfig
                .builder()
                .setThinkingConfig(
                    thinkingConfig = ThinkingConfig
                        .Builder()
                        .setThinkingBudget(0)
                        .build()
                )
                .build()
        )
    }

    override fun processTextForTranslation(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        callback: (Result<Translation>) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            var lastException: Exception? = null

            for (attempt in 0 until MAX_RETRIES) {
                try {
                    val result = withTimeout(TIMEOUT_MS) {
                        val prompt = createTranslationPrompt(text, sourceLanguage, targetLanguage)
                        val response = generativeModel.generateContent(prompt)
                        val responseText = response.text ?: throw Exception("No response from AI")
                        parseTranslationResponse(responseText, text, sourceLanguage, targetLanguage)
                    }

                    callback(Result.success(result))
                    return@launch

                } catch (e: TimeoutCancellationException) {
                    lastException = Exception("AI request timed out after ${TIMEOUT_MS}ms")
                } catch (e: IOException) {
                    lastException = Exception("Network error during AI processing: ${e.message}")
                } catch (e: Exception) {
                    // Check if this is a server error (5xx) that should be retried
                    val isRetryable = e.message?.contains("502") == true ||
                                    e.message?.contains("503") == true ||
                                    e.message?.contains("504") == true ||
                                    e.message?.contains("500") == true ||
                                    e.message?.contains("Server Error") == true

                    if (isRetryable && attempt < MAX_RETRIES - 1) {
                        val delayMs = INITIAL_RETRY_DELAY_MS * 2.0.pow(attempt.toDouble()).toLong()
                        delay(delayMs)
                        continue
                    } else {
                        lastException = e
                        break
                    }
                }
            }

            callback(Result.failure(lastException ?: Exception("AI processing failed after $MAX_RETRIES attempts")))
        }
    }

    private fun createTranslationPrompt(text: String, sourceLang: String, targetLang: String): String {
        // Validate and normalize language codes
        val validatedSource = LanguageValidator.normalizeLanguageCode(sourceLang)
        val validatedTarget = LanguageValidator.normalizeLanguageCode(targetLang)

        val sourceDisplayName = LanguageValidator.getDisplayName(validatedSource)
        val targetDisplayName = LanguageValidator.getDisplayName(validatedTarget)

        return """
            You are a language learning assistant. Analyze the provided text and extract key vocabulary and expressions for translation.

            Language Information:
            - Source Language: ${if (validatedSource == "auto") "Auto-detect" else sourceDisplayName} ($validatedSource)
            - Target Language: $targetDisplayName ($validatedTarget)

            Return a JSON object with this exact structure:
            {
              "originalText": "the full original text",
              "translatedText": "complete translation in $validatedTarget",
              "originalTextPronunciations": "pronunciation guide if applicable, or null",
              "sourceLanguage": "$validatedSource",
              "targetLanguage": "$validatedTarget",
              "words": [
                {
                  "word": "important word",
                  "normalizedWord": "lowercase version for matching",
                  "pronunciation": "phonetic if applicable",
                  "translations": ["translation1", "translation2"]
                }
              ],
              "expressions": [
                {
                  "sourceExpression": "common phrase or idiom",
                  "translatedExpression": "translation of the expression",
                  "normalizedExpression": "lowercase version for matching",
                  "pronunciation": "phonetic if applicable"
                }
              ]
            }

            Guidelines:
            - If source language is "auto", analyze the text and determine the most likely language
            - Extract all of the most important words that a learner should know
            - Extract all useful expressions/phrases
            - Provide accurate translations to $targetDisplayName
            - Include pronunciation where helpful (use phonetic spelling)
            - Focus on vocabulary that appears in the text or is contextually relevant
            - normalizedWord/normalizedExpression should be lowercase without punctuation for frequency tracking

            Text to analyze:
            $text
        """.trimIndent()
    }

    private fun parseTranslationResponse(
        responseText: String,
        originalText: String,
        sourceLanguage: String,
        targetLanguage: String
    ): Translation {
        // Validate and normalize the language codes
        val validatedSource = LanguageValidator.normalizeLanguageCode(sourceLanguage)
        val validatedTarget = LanguageValidator.normalizeLanguageCode(targetLanguage)
        try {
            // Clean the response text (remove markdown code blocks if present)
            val cleanText = responseText
                .removePrefix("```json")
                .removeSuffix("```")
                .trim()

            val jsonElement = Json.parseToJsonElement(cleanText)
            val jsonObject = jsonElement.jsonObject

            val translatedText = jsonObject["translatedText"]?.jsonPrimitive?.content
                ?: throw Exception("Missing translatedText")

            val originalTextPronunciations = jsonObject["originalTextPronunciations"]?.jsonPrimitive?.content

            // Parse words
            val wordsArray = jsonObject["words"]?.jsonArray ?: throw Exception("Missing words array")
            val words = wordsArray.map { wordElement ->
                val wordObj = wordElement.jsonObject
                WordDetail(
                    word = wordObj["word"]?.jsonPrimitive?.content ?: "",
                    normalizedWord = wordObj["normalizedWord"]?.jsonPrimitive?.content ?: "",
                    pronunciation = wordObj["pronunciation"]?.jsonPrimitive?.content,
                    translations = wordObj["translations"]?.jsonArray?.map { it.jsonPrimitive.content } ?: emptyList()
                )
            }

            // Parse expressions
            val expressionsArray = jsonObject["expressions"]?.jsonArray ?: throw Exception("Missing expressions array")
            val expressions = expressionsArray.map { exprElement ->
                val exprObj = exprElement.jsonObject
                ExpressionExample(
                    sourceExpression = exprObj["sourceExpression"]?.jsonPrimitive?.content ?: "",
                    translatedExpression = exprObj["translatedExpression"]?.jsonPrimitive?.content ?: "",
                    normalizedExpression = exprObj["normalizedExpression"]?.jsonPrimitive?.content ?: "",
                    pronunciation = exprObj["pronunciation"]?.jsonPrimitive?.content
                )
            }

            val calendar = Calendar.getInstance()

            // If you need a formatted date and time string:
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val formattedDateTime = dateFormat.format(calendar.time)
            return Translation(
                id = generateId(),
                originalText = originalText,
                translatedText = translatedText,
                originalTextPronunciations = originalTextPronunciations,
                sourceLanguage = validatedSource,
                targetLanguage = validatedTarget,
                timestamp = formattedDateTime,
                source = "text_processing",
                isStarred = false,
                words = words,
                expressions = expressions
            )

        } catch (e: Exception) {
            throw Exception("Failed to parse AI response: ${e.message}")
        }
    }

    private fun generateId(): String {
        return java.util.UUID.randomUUID().toString()
    }

    override fun isAvailable(): Boolean {
        return try {
            true
        } catch (e: Exception) {
            false
        }
    }
}