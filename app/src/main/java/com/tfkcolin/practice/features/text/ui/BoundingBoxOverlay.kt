package com.tfkcolin.practice.features.text.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.sp

@Composable
fun BoundingBoxOverlay(
    modifier: Modifier = Modifier,
    textBlocks: List<RecognizedTextBlock>,
    selectedTextBlock: RecognizedTextBlock? = null,
    onTextBlockClick: (RecognizedTextBlock) -> Unit
) {
    Canvas(modifier = modifier) {
        textBlocks.forEach { textBlock ->
            val box = textBlock.boundingBox
            val isSelected = selectedTextBlock?.text == textBlock.text

            // Draw bounding box
            drawRect(
                color = if (isSelected) Color.Green else Color.Red,
                topLeft = Offset(box.left.toFloat(), box.top.toFloat()),
                size = Size(box.width().toFloat(), box.height().toFloat()),
                style = Stroke(width = if (isSelected) 4f else 2f)
            )

            // Draw text label using native canvas
            drawIntoCanvas { canvas ->
                val paint = android.graphics.Paint().apply {
                    color = if (isSelected) android.graphics.Color.GREEN else android.graphics.Color.RED
                    textSize = 12.sp.toPx()
                    isAntiAlias = true
                }
                val text = textBlock.text.take(20) + if (textBlock.text.length > 20) "..." else ""
                canvas.nativeCanvas.drawText(
                    text,
                    box.left.toFloat(),
                    box.top.toFloat() - 10f,
                    paint
                )
            }
        }
    }

    // Handle tap events
    Box(modifier = modifier.pointerInput(Unit) {
        detectTapGestures { offset ->
            textBlocks.forEach { textBlock ->
                val box = textBlock.boundingBox
                if (offset.x >= box.left && offset.x <= box.right &&
                    offset.y >= box.top && offset.y <= box.bottom) {
                    onTextBlockClick(textBlock)
                    return@detectTapGestures
                }
            }
        }
    })
}