package com.tfkcolin.practice.features.text.ui

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import com.tfkcolin.practice.features.text.domain.ExtractedTextValidationViewModel
import com.tfkcolin.practice.ui.theme.*
import java.io.File
import androidx.core.net.toUri

@Composable
fun AudioPlayerSection(audioPath: String) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer by remember { mutableStateOf<android.media.MediaPlayer?>(null) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = AccentBlue.copy(alpha = 0.1f))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Listen to Audio Source",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = AccentBlue
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                IconButton(
                    onClick = {
                        if (isPlaying) {
                            mediaPlayer?.pause()
                            mediaPlayer?.seekTo(0)
                            isPlaying = false
                        } else {
                            mediaPlayer?.release()
                            mediaPlayer = android.media.MediaPlayer().apply {
                                try {
                                    // Handle both content URIs and file paths
                                    val uri = if (audioPath.startsWith("content://")) {
                                        Uri.parse(audioPath)
                                    } else {
                                        Uri.fromFile(File(audioPath))
                                    }
                                    setDataSource(context, uri)
                                    prepare()
                                    start()
                                    setOnCompletionListener {
                                        isPlaying = false
                                    }
                                    isPlaying = true
                                } catch (e: Exception) {
                                    // Handle error
                                    isPlaying = false
                                }
                            }
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                        contentDescription = if (isPlaying) "Pause" else "Play",
                        tint = AccentBlue,
                        modifier = Modifier.size(48.dp)
                    )
                }

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = if (isPlaying) "Playing..." else "Tap to play",
                    style = MaterialTheme.typography.bodyMedium,
                    color = DarkGray
                )
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            mediaPlayer?.release()
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExtractedTextValidationScreen(
    inputType: String,
    extractedText: String,
    detectedLanguage: String?,
    sourceInfo: String?,
    onProceed: (String) -> Unit,
    onReject: () -> Unit,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: ExtractedTextValidationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Initialize text when screen loads
    LaunchedEffect(extractedText) {
        viewModel.initializeText(extractedText)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Review Extracted Text",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = PrimaryBlack
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(16.dp))

            // Header text
            Text(
                text = "Please review and edit the extracted text if needed",
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Source: ${inputType.replaceFirstChar { it.uppercase() }}",
                style = MaterialTheme.typography.bodyMedium,
                color = MediumGray
            )

            detectedLanguage?.let {
                Text(
                    text = "Detected Language: $it",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MediumGray
                )
            }

            sourceInfo?.let { info ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Source: $info",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MediumGray
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Show source media for verification
            when (inputType) {
                "image" -> sourceInfo?.let { imageUri ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        colors = CardDefaults.cardColors(containerColor = LightGray)
                    ) {
                        AsyncImage(
                            model = imageUri.toUri(),
                            contentDescription = "Source image",
                            contentScale = ContentScale.Fit,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                }
                "audio" -> sourceInfo?.let { audioPath ->
                    AudioPlayerSection(audioPath)
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }

            // Editable text field
            OutlinedTextField(
                value = uiState.text,
                onValueChange = { viewModel.updateText(it) },
                label = { Text("Extracted Text") },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .height(200.dp),
                maxLines = 10,
                textStyle = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.weight(1f))

            // Action buttons
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = onReject,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text("Reject")
                }

                Button(
                    onClick = {
                        val validatedText = viewModel.getValidatedText()
                        if (validatedText.isNotBlank()) {
                            onProceed(validatedText)
                        }
                    },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = AccentGreen),
                    enabled = uiState.text.isNotBlank()
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text("Proceed to Processing")
                }
            }
        }
    }
}