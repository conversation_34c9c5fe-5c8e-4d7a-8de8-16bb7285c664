package com.tfkcolin.practice.features.text.data

import android.content.Context
import android.net.Uri
import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader

interface DocumentTextProcessingService {

    /**
     * Extract text from a document URI
     * @param context Android context
     * @param uri The URI of the document
     * @return Result containing extracted text or error
     */
    suspend fun extractTextFromDocument(context: Context, uri: Uri): Result<String>

    /**
     * Split text into self-contained paragraphs for translation
     * @param text The full text to split
     * @param maxLength Maximum length per paragraph chunk
     * @return List of text chunks
     */
    fun splitIntoParagraphs(text: String, maxLength: Int = 1000): List<String>

    /**
     * Check if the service supports the given MIME type
     * @param mimeType The MIME type to check
     * @return true if supported
     */
    fun isSupportedMimeType(mimeType: String?): Boolean
}

class BasicDocumentTextProcessingService : DocumentTextProcessingService {

    companion object {
        private const val TAG = "DocumentTextProcessingService"
        private const val MAX_CHUNK_LENGTH = 1500 // Increased from 1000 for fewer API calls
        private const val CONTEXT_OVERLAP = 100 // Characters to overlap between chunks for context
        private val SUPPORTED_MIME_TYPES = setOf(
            "text/plain",
            "text/markdown"
        )
    }

    override suspend fun extractTextFromDocument(context: Context, uri: Uri): Result<String> {
        Log.d(TAG, "Starting text extraction for URI: $uri")
        return try {
            val mimeType = context.contentResolver.getType(uri)
            Log.d(TAG, "MIME type for URI $uri: $mimeType")

            when {
                mimeType == "text/plain" || mimeType == "text/markdown" -> {
                    Log.d(TAG, "MIME type supported, extracting text")
                    extractTextFromTxtFile(context, uri)
                }
                else -> {
                    Log.w(TAG, "Unsupported MIME type: $mimeType")
                    Result.failure(Exception("Unsupported file type: $mimeType. Only TXT and Markdown files are supported."))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during text extraction", e)
            Result.failure(e)
        }
    }

    private suspend fun extractTextFromTxtFile(context: Context, uri: Uri): Result<String> {
        Log.d(TAG, "Opening input stream for URI: $uri")
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: return Result.failure<String>(Exception("Could not open file")).also {
                    Log.e(TAG, "Failed to open input stream for URI: $uri")
                }

            Log.d(TAG, "Reading text from input stream")
            val reader = BufferedReader(InputStreamReader(inputStream))
            val text = reader.use { it.readText() }
            Log.d(TAG, "Text read successfully, length: ${text.length}")

            if (text.isNotBlank()) {
                Log.d(TAG, "Text is not blank, returning success")
                Result.success(text.trim())
            } else {
                Log.w(TAG, "Text is blank or empty")
                Result.failure(Exception("File is empty"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reading text from file", e)
            Result.failure(e)
        }
    }

    override fun splitIntoParagraphs(text: String, maxLength: Int): List<String> {
        if (text.isBlank()) return emptyList()

        // First, try to split on paragraph boundaries (double newlines)
        val rawParagraphs = text.split(Regex("\\n\\s*\\n")).filter { it.isNotBlank() }

        return if (rawParagraphs.size > 1) {
            // If we have multiple paragraphs, use paragraph-aware splitting
            splitByParagraphsWithContext(rawParagraphs, maxLength)
        } else {
            // Fall back to sentence-based splitting with context
            splitBySentencesWithContext(text, maxLength)
        }
    }

    /**
     * Split text by paragraphs while maintaining context between chunks
     */
    private fun splitByParagraphsWithContext(paragraphs: List<String>, maxLength: Int): List<String> {
        val chunks = mutableListOf<String>()
        var currentChunk = StringBuilder()
        var previousChunkEnd = ""

        for ((index, paragraph) in paragraphs.withIndex()) {
            val paragraphWithSpace = if (currentChunk.isNotEmpty()) " $paragraph" else paragraph

            // Check if adding this paragraph would exceed the limit
            if (currentChunk.length + paragraphWithSpace.length > maxLength) {
                if (currentChunk.isNotEmpty()) {
                    // Add overlap from previous chunk for context
                    if (previousChunkEnd.isNotEmpty() && currentChunk.length + CONTEXT_OVERLAP <= maxLength) {
                        val overlapText = previousChunkEnd.takeLast(CONTEXT_OVERLAP)
                        currentChunk.insert(0, "...$overlapText ")
                    }

                    chunks.add(currentChunk.toString().trim())
                    previousChunkEnd = currentChunk.toString().takeLast(CONTEXT_OVERLAP)
                    currentChunk = StringBuilder()
                }

                // If the paragraph itself is too long, split it by sentences
                if (paragraph.length > maxLength) {
                    val sentenceChunks = splitBySentencesWithContext(paragraph, maxLength)
                    chunks.addAll(sentenceChunks.dropLast(1)) // Add all but the last
                    currentChunk = StringBuilder(sentenceChunks.lastOrNull() ?: "")
                } else {
                    currentChunk.append(paragraph)
                }
            } else {
                currentChunk.append(paragraphWithSpace)
            }
        }

        if (currentChunk.isNotEmpty()) {
            // Add overlap for the final chunk too
            if (previousChunkEnd.isNotEmpty() && currentChunk.length + CONTEXT_OVERLAP <= maxLength) {
                val overlapText = previousChunkEnd.takeLast(CONTEXT_OVERLAP)
                currentChunk.insert(0, "...$overlapText ")
            }
            chunks.add(currentChunk.toString().trim())
        }

        return chunks.filter { it.isNotBlank() }
    }

    /**
     * Split text by sentences while maintaining context and avoiding mid-sentence breaks
     */
    private fun splitBySentencesWithContext(text: String, maxLength: Int): List<String> {
        val chunks = mutableListOf<String>()

        // Split into sentences, but be more careful about what constitutes a sentence
        val sentences = text.split(Regex("(?<=[.!?])\\s+")).filter { it.isNotBlank() }

        if (sentences.isEmpty()) {
            return listOf(text.take(maxLength))
        }

        var currentChunk = StringBuilder()
        var previousChunkEnd = ""
        var sentenceBuffer = mutableListOf<String>()

        for (sentence in sentences) {
            sentenceBuffer.add(sentence)

            // Try to create a meaningful chunk with multiple sentences if possible
            val potentialChunk = if (currentChunk.isNotEmpty()) {
                "${currentChunk.toString().trim()} ${sentenceBuffer.joinToString(" ")}"
            } else {
                sentenceBuffer.joinToString(" ")
            }

            if (potentialChunk.length > maxLength) {
                // If we've accumulated too much, finalize the current chunk
                if (currentChunk.isNotEmpty()) {
                    // Add some context from the previous chunk
                    if (previousChunkEnd.isNotEmpty() && currentChunk.length + CONTEXT_OVERLAP <= maxLength) {
                        val overlapText = previousChunkEnd.takeLast(CONTEXT_OVERLAP)
                        val chunkWithOverlap = "...$overlapText ${currentChunk.toString().trim()}"
                        if (chunkWithOverlap.length <= maxLength) {
                            chunks.add(chunkWithOverlap)
                        } else {
                            chunks.add(currentChunk.toString().trim())
                        }
                    } else {
                        chunks.add(currentChunk.toString().trim())
                    }

                    previousChunkEnd = currentChunk.toString().takeLast(CONTEXT_OVERLAP)
                    currentChunk = StringBuilder()
                }

                // Handle the current sentence buffer
                val sentenceText = sentenceBuffer.joinToString(" ")
                if (sentenceText.length > maxLength) {
                    // Split long sentence by words as last resort
                    val wordChunks = splitLongSentence(sentenceText, maxLength)
                    chunks.addAll(wordChunks.dropLast(1))
                    currentChunk = StringBuilder(wordChunks.lastOrNull() ?: "")
                } else {
                    currentChunk = StringBuilder(sentenceText)
                }

                sentenceBuffer.clear()
            } else {
                // Keep accumulating in current chunk
                currentChunk = StringBuilder(potentialChunk)
                sentenceBuffer.clear()
            }
        }

        // Handle any remaining content
        if (currentChunk.isNotEmpty()) {
            val finalChunk = currentChunk.toString().trim()
            if (previousChunkEnd.isNotEmpty() && finalChunk.length + CONTEXT_OVERLAP <= maxLength) {
                val overlapText = previousChunkEnd.takeLast(CONTEXT_OVERLAP)
                chunks.add("...$overlapText $finalChunk")
            } else {
                chunks.add(finalChunk)
            }
        }

        return chunks.filter { it.isNotBlank() }
    }

    /**
     * Split a long sentence by words when all other methods fail
     */
    private fun splitLongSentence(sentence: String, maxLength: Int): List<String> {
        val words = sentence.split("\\s+".toRegex()).filter { it.isNotBlank() }
        val chunks = mutableListOf<String>()
        var currentChunk = StringBuilder()

        for (word in words) {
            val wordWithSpace = if (currentChunk.isNotEmpty()) " $word" else word

            if (currentChunk.length + wordWithSpace.length > maxLength) {
                if (currentChunk.isNotEmpty()) {
                    chunks.add(currentChunk.toString().trim())
                    currentChunk = StringBuilder()
                }

                // If a single word is too long, we have to include it anyway
                if (word.length > maxLength) {
                    chunks.add(word.take(maxLength)) // Truncate as last resort
                } else {
                    currentChunk.append(word)
                }
            } else {
                currentChunk.append(wordWithSpace)
            }
        }

        if (currentChunk.isNotEmpty()) {
            chunks.add(currentChunk.toString().trim())
        }

        return chunks.filter { it.isNotBlank() }
    }

    override fun isSupportedMimeType(mimeType: String?): Boolean {
        val isSupported = mimeType in SUPPORTED_MIME_TYPES
        // Only log in non-test environments to avoid mocking issues
        if (!isRunningTests()) {
            Log.d(TAG, "Checking MIME type: $mimeType, supported: $isSupported")
        }
        return isSupported
    }

    private fun isRunningTests(): Boolean {
        return try {
            Class.forName("org.junit.Test") != null ||
            Class.forName("androidx.test.ext.junit.runners.AndroidJUnit4") != null
        } catch (e: ClassNotFoundException) {
            false
        }
    }
}