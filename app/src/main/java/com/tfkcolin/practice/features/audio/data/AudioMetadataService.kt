package com.tfkcolin.practice.features.audio.data

import android.media.MediaMetadataRetriever
import android.util.Log
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for extracting metadata from audio files
 */
interface AudioMetadataService {

    /**
     * Extract metadata from an audio file
     * @param audioFile The audio file to analyze
     * @return AudioMetadata containing duration, size, and other properties
     */
    fun extractMetadata(audioFile: File): Result<AudioMetadata>

    /**
     * Check if an audio file exceeds the maximum allowed duration
     * @param metadata The metadata to check
     * @param maxDurationMs Maximum allowed duration in milliseconds
     * @return true if the file is too long
     */
    fun isTooLong(metadata: AudioMetadata, maxDurationMs: Long = MAX_AUDIO_DURATION_MS): Boolean

    /**
     * Calculate number of segments needed for an audio file
     * @param metadata The metadata to analyze
     * @param segmentDurationMs Duration of each segment in milliseconds
     * @return Number of segments needed
     */
    fun calculateSegmentCount(metadata: AudioMetadata, segmentDurationMs: Long = SEGMENT_DURATION_MS): Int

    companion object {
        const val MAX_AUDIO_DURATION_MS = 10 * 60 * 1000L // 10 minutes
        const val SEGMENT_DURATION_MS = 5 * 60 * 1000L // 5 minutes per segment
        const val SEGMENT_OVERLAP_MS = 30 * 1000L // 30 seconds overlap between segments
    }
}

data class AudioMetadata(
    val durationMs: Long,
    val fileSizeBytes: Long,
    val sampleRate: Int? = null,
    val channels: Int? = null,
    val bitrate: Int? = null,
    val mimeType: String? = null
)

@Singleton
class AndroidAudioMetadataService @Inject constructor() : AudioMetadataService {

    companion object {
        private const val TAG = "AudioMetadataService"
    }

    override fun extractMetadata(audioFile: File): Result<AudioMetadata> {
        if (!audioFile.exists()) {
            return Result.failure(IOException("Audio file does not exist: ${audioFile.absolutePath}"))
        }

        if (!audioFile.canRead()) {
            return Result.failure(IOException("Cannot read audio file: ${audioFile.absolutePath}"))
        }

        val retriever = MediaMetadataRetriever()
        return try {
            retriever.setDataSource(audioFile.absolutePath)

            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val durationMs = durationStr?.toLongOrNull() ?: 0L

            val channelsStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_NUM_TRACKS)
            val channels = channelsStr?.toIntOrNull()

            val bitrateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)
            val bitrate = bitrateStr?.toIntOrNull()

            val mimeType = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)

            val metadata = AudioMetadata(
                durationMs = durationMs,
                fileSizeBytes = audioFile.length(),
                channels = channels,
                bitrate = bitrate,
                mimeType = mimeType
            )

            Log.d(TAG, "Extracted metadata for ${audioFile.name}: duration=${durationMs}ms, size=${audioFile.length()} bytes")
            Result.success(metadata)

        } catch (e: Exception) {
            Log.e(TAG, "Error extracting metadata from ${audioFile.name}", e)
            Result.failure(IOException("Failed to extract audio metadata: ${e.message}", e))
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.w(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
    }

    override fun isTooLong(metadata: AudioMetadata, maxDurationMs: Long): Boolean {
        return metadata.durationMs > maxDurationMs
    }

    override fun calculateSegmentCount(metadata: AudioMetadata, segmentDurationMs: Long): Int {
        if (metadata.durationMs <= segmentDurationMs) {
            return 1
        }

        val segmentCount = Math.ceil(metadata.durationMs.toDouble() / segmentDurationMs.toDouble()).toInt()
        return segmentCount
    }
}