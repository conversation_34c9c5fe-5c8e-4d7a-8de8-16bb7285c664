package com.tfkcolin.practice.features.auth.ui

import android.util.Patterns
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.auth.domain.AuthViewModel
import com.tfkcolin.practice.features.auth.domain.AuthState
import com.tfkcolin.practice.ui.components.*
import com.tfkcolin.practice.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthScreen(
    authViewModel: AuthViewModel,
    modifier: Modifier = Modifier
) {
    val authState by authViewModel.authState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    var isLoginMode by remember { mutableStateOf(true) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordConfirm by remember { mutableStateOf("") }
    var displayName by remember { mutableStateOf("") }
    var showPasswordField by remember { mutableStateOf(false) }

    // Validation states
    var emailError by remember { mutableStateOf<String?>(null) }
    var passwordError by remember { mutableStateOf<String?>(null) }
    var passwordConfirmError by remember { mutableStateOf<String?>(null) }

    // Clear fields when switching modes
    LaunchedEffect(isLoginMode) {
        email = ""
        password = ""
        passwordConfirm = ""
        displayName = ""
        showPasswordField = false
        emailError = null
        passwordError = null
        passwordConfirmError = null
        authViewModel.clearError()
    }

    // Validation functions
    fun validateEmail(): Boolean {
        return when {
            email.isEmpty() -> {
                emailError = "Email is required"
                false
            }
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                emailError = "Please enter a valid email address"
                false
            }
            else -> {
                emailError = null
                true
            }
        }
    }

    fun validatePassword(): Boolean {
        return when {
            password.isEmpty() -> {
                passwordError = "Password is required"
                false
            }
            password.length < 6 -> {
                passwordError = "Password must be at least 6 characters"
                false
            }
            else -> {
                passwordError = null
                true
            }
        }
    }

    fun validatePasswordConfirm(): Boolean {
        return when {
            passwordConfirm.isEmpty() -> {
                passwordConfirmError = "Please confirm your password"
                false
            }
            passwordConfirm != password -> {
                passwordConfirmError = "Passwords do not match"
                false
            }
            else -> {
                passwordConfirmError = null
                true
            }
        }
    }

    fun validateForm(): Boolean {
        val emailValid = validateEmail()
        val passwordValid = validatePassword()
        val confirmValid = if (!isLoginMode) validatePasswordConfirm() else true
        return emailValid && passwordValid && confirmValid
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(PrimaryWhite)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Logo section
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(PrimaryBlack),
                contentAlignment = Alignment.Center
            ) {
                // Curved logo shape - simplified representation
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(PrimaryWhite)
                )
            }

            Spacer(modifier = Modifier.height(48.dp))

            Text(
                text = if (isLoginMode) "Welcome Back" else "Create Account",
                fontSize = 24.sp,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // Email input field
            ProfessionalTextField(
                value = email,
                onValueChange = { email = it },
                placeholder = "Enter your email",
                isError = emailError != null,
                errorMessage = emailError,
                leadingIcon = Icons.Default.Email,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Password field (only show in sign up mode or when needed)
            if (!isLoginMode || password.isNotEmpty() || showPasswordField) {
                ProfessionalTextField(
                    value = password,
                    onValueChange = { password = it },
                    placeholder = "Enter your password",
                    isError = passwordError != null,
                    errorMessage = passwordError,
                    leadingIcon = Icons.Default.Lock
                )
                Spacer(modifier = Modifier.height(16.dp))
            }

            // Additional fields for sign up mode
            if (!isLoginMode) {
                ProfessionalTextField(
                    value = passwordConfirm,
                    onValueChange = { passwordConfirm = it },
                    placeholder = "Confirm your password",
                    isError = passwordConfirmError != null,
                    errorMessage = passwordConfirmError,
                    leadingIcon = Icons.Default.Lock
                )
                Spacer(modifier = Modifier.height(16.dp))

                ProfessionalTextField(
                    value = displayName,
                    onValueChange = { displayName = it },
                    placeholder = "Display Name (Optional)",
                    leadingIcon = Icons.Default.Person
                )
                Spacer(modifier = Modifier.height(16.dp))
            }

            // Error and success messages
            if (authState.error != null) {
                Column(
                    modifier = Modifier.padding(vertical = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = authState.error!!,
                        color = ErrorRed,
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                    if (authState.errorSuggestion != null) {
                        Text(
                            text = authState.errorSuggestion!!,
                            color = ErrorRed.copy(alpha = 0.8f),
                            fontSize = 12.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            if (authState.successMessage != null) {
                Text(
                    text = authState.successMessage!!,
                    color = SuccessGreen,
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Continue button
            ProfessionalButton(
                text = if (isLoginMode && password.isEmpty()) "Continue" else if (isLoginMode) "Log In" else "Sign Up",
                onClick = {
                    if (isLoginMode && password.isEmpty() && !showPasswordField) {
                        // Show password field for login without setting invalid value
                        showPasswordField = true
                    } else {
                        if (validateForm()) {
                            if (isLoginMode) {
                                authViewModel.signIn(email, password.trim())
                            } else {
                                authViewModel.signUp(email, password.trim(), displayName.takeIf { it.isNotEmpty() })
                            }
                        }
                    }
                },
                enabled = !authState.isLoading && email.isNotEmpty() &&
                          (password.isEmpty() || (password.isNotEmpty() && (isLoginMode || passwordConfirm.isNotEmpty()))),
                isLoading = authState.isLoading,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            // Divider with "or"
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = DividerDefaults.Thickness,
                    color = MediumGray
                )
                Text(
                    text = "or",
                    color = DarkGray,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = DividerDefaults.Thickness,
                    color = MediumGray
                )
            }

            // Google Sign-In button
            SocialSignInButton(
                text = "Continue with Google",
                icon = Icons.Default.AccountCircle,
                onClick = {
                    authViewModel.signInWithGoogle(context)
                },
                modifier = Modifier.padding(vertical = 4.dp)
            )

            // Apple Sign-In button (placeholder)
            SocialSignInButton(
                text = "Continue with Apple",
                icon = Icons.Default.Phone,
                onClick = {
                    // TODO: Implement Apple Sign-In (placeholder)
                },
                backgroundColor = PrimaryBlack,
                textColor = PrimaryWhite,
                iconColor = PrimaryWhite,
                modifier = Modifier.padding(vertical = 4.dp)
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Mode switch and forgot password
            if (isLoginMode && password.isNotEmpty()) {
                TextButton(
                    onClick = { authViewModel.resetPassword(email) },
                    enabled = !authState.isLoading && email.isNotEmpty()
                ) {
                    Text(
                        text = "Forgot Password?",
                        color = AccentBlue,
                        fontSize = 14.sp
                    )
                }
            }

            TextButton(
                onClick = {
                    isLoginMode = !isLoginMode
                    password = ""
                    passwordConfirm = ""
                    displayName = ""
                },
                enabled = !authState.isLoading
            ) {
                Text(
                    text = if (isLoginMode) "Don't have an account? Sign Up" else "Already have an account? Log In",
                    color = AccentBlue,
                    fontSize = 14.sp
                )
            }
        }
    }
}