package com.tfkcolin.practice.features.auth.data

import android.content.Context
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialException
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.FirebaseAuthInvalidUserException
import com.google.firebase.auth.FirebaseAuthUserCollisionException
import com.google.firebase.auth.FirebaseAuthWeakPasswordException
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.userProfileChangeRequest
import com.tfkcolin.practice.features.auth.domain.AuthState
import com.tfkcolin.practice.features.auth.domain.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.tasks.await
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth
) {
    companion object {
        private const val GOOGLE_WEB_CLIENT_ID = "11021412400-hhqea30me6l5gjd3ihncq8hqlposb9hf.apps.googleusercontent.com"
    }

    enum class AuthErrorType {
        INVALID_CREDENTIALS,
        USER_NOT_FOUND,
        EMAIL_ALREADY_EXISTS,
        WEAK_PASSWORD,
        NETWORK_ERROR,
        SERVICE_UNAVAILABLE,
        INVALID_EMAIL,
        TOO_MANY_REQUESTS,
        UNKNOWN_ERROR
    }

    data class AuthError(
        val type: AuthErrorType,
        val message: String,
        val suggestion: String? = null
    )

    private fun categorizeFirebaseError(exception: Exception): AuthError {
        return when (exception) {
            is FirebaseAuthInvalidCredentialsException -> AuthError(
                AuthErrorType.INVALID_CREDENTIALS,
                "Invalid email or password",
                "Please check your credentials and try again"
            )
            is FirebaseAuthInvalidUserException -> AuthError(
                AuthErrorType.USER_NOT_FOUND,
                "Account not found",
                "This email is not registered. Please sign up first"
            )
            is FirebaseAuthUserCollisionException -> AuthError(
                AuthErrorType.EMAIL_ALREADY_EXISTS,
                "Account already exists",
                "This email is already in use. Try signing in instead"
            )
            is FirebaseAuthWeakPasswordException -> AuthError(
                AuthErrorType.WEAK_PASSWORD,
                "Password is too weak",
                "Password must be at least 6 characters long"
            )
            is FirebaseAuthException -> when (exception.errorCode) {
                "ERROR_INVALID_EMAIL" -> AuthError(
                    AuthErrorType.INVALID_EMAIL,
                    "Invalid email format",
                    "Please enter a valid email address"
                )
                "ERROR_TOO_MANY_REQUESTS" -> AuthError(
                    AuthErrorType.TOO_MANY_REQUESTS,
                    "Too many attempts",
                    "Please wait a moment before trying again"
                )
                "ERROR_OPERATION_NOT_ALLOWED" -> AuthError(
                    AuthErrorType.SERVICE_UNAVAILABLE,
                    "Service temporarily unavailable",
                    "Please try again later"
                )
                else -> AuthError(
                    AuthErrorType.UNKNOWN_ERROR,
                    "Authentication failed",
                    "Please try again or contact support"
                )
            }
            is ConnectException, is UnknownHostException -> AuthError(
                AuthErrorType.NETWORK_ERROR,
                "No internet connection",
                "Please check your connection and try again"
            )
            is SocketTimeoutException -> AuthError(
                AuthErrorType.NETWORK_ERROR,
                "Connection timeout",
                "Please check your connection and try again"
            )
            is IOException -> AuthError(
                AuthErrorType.NETWORK_ERROR,
                "Network error",
                "Please check your connection and try again"
            )
            else -> AuthError(
                AuthErrorType.UNKNOWN_ERROR,
                exception.localizedMessage ?: "An unexpected error occurred",
                "Please try again or contact support"
            )
        }
    }
    private val _authState = MutableStateFlow<AuthState>(AuthState())
    val authState: StateFlow<AuthState> = _authState

    init {
        // Set initial loading state
        _authState.value = _authState.value.copy(isLoading = true)

        firebaseAuth.addAuthStateListener { auth ->
            val firebaseUser = auth.currentUser
            val user = firebaseUser?.let { mapFirebaseUserToUser(it) }
            _authState.value = AuthState(
                isLoading = false, // Auth state determined
                isAuthenticated = firebaseUser != null,
                user = user
            )
        }
    }

    suspend fun signUp(
        email: String,
        password: String,
        displayName: String? = null
    ): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)
            val result = firebaseAuth.createUserWithEmailAndPassword(email, password).await()
            result.user?.let { firebaseUser ->
                // Update display name if provided
                if (!displayName.isNullOrEmpty()) {
                    firebaseUser.updateProfile(
                        userProfileChangeRequest {
                            this.displayName = displayName
                        }
                    ).await()
                }
            }
            val successMessage = if (displayName.isNullOrEmpty()) "Account created successfully!" else "Account created successfully!"
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                successMessage = successMessage
            )
            Result.success(Unit)
        } catch (e: Exception) {
            val authError = categorizeFirebaseError(e)
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = authError.message,
                errorSuggestion = authError.suggestion,
                successMessage = null
            )
            Result.failure(e)
        }
    }

    suspend fun signIn(email: String, password: String): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)
            firebaseAuth.signInWithEmailAndPassword(email, password).await()
            val currentUser = firebaseAuth.currentUser
            val welcomeMessage = if (currentUser?.displayName != null) {
                "Welcome back, ${currentUser.displayName}!"
            } else {
                "Welcome back!"
            }
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                errorSuggestion = null,
                successMessage = welcomeMessage
            )
            Result.success(Unit)
        } catch (e: Exception) {
            val authError = categorizeFirebaseError(e)
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = authError.message,
                errorSuggestion = authError.suggestion,
                successMessage = null
            )
            Result.failure(e)
        }
    }

    suspend fun signOut(): Result<Unit> {
        return try {
            firebaseAuth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun resetPassword(email: String): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null, errorSuggestion = null, successMessage = null)
            firebaseAuth.sendPasswordResetEmail(email).await()
            _authState.value = _authState.value.copy(
                isLoading = false,
                successMessage = "Password reset email sent to $email"
            )
            Result.success(Unit)
        } catch (e: Exception) {
            val authError = categorizeFirebaseError(e)
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = authError.message,
                errorSuggestion = authError.suggestion,
                successMessage = null
            )
            Result.failure(e)
        }
    }

    fun getCurrentUser(): User? {
        return firebaseAuth.currentUser?.let { mapFirebaseUserToUser(it) }
    }

    suspend fun signInWithGoogle(context: Context): Result<Unit> {
        return try {
            _authState.value = _authState.value.copy(isLoading = true, error = null)

            val credentialManager = CredentialManager.create(context)

            val googleIdOption = GetGoogleIdOption.Builder()
                .setFilterByAuthorizedAccounts(false)
                .setServerClientId(GOOGLE_WEB_CLIENT_ID)
                .build()

            val request = GetCredentialRequest.Builder()
                .addCredentialOption(googleIdOption)
                .build()

            val result = credentialManager.getCredential(
                request = request,
                context = context,
            )

            val credential = result.credential
            val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
            val googleIdToken = googleIdTokenCredential.idToken

            val firebaseCredential = GoogleAuthProvider.getCredential(googleIdToken, null)
            firebaseAuth.signInWithCredential(firebaseCredential).await()

            val currentUser = firebaseAuth.currentUser
            val welcomeMessage = if (currentUser?.displayName != null) {
                "Welcome, ${currentUser.displayName}!"
            } else {
                "Welcome!"
            }

            _authState.value = _authState.value.copy(
                isLoading = false,
                error = null,
                errorSuggestion = null,
                successMessage = welcomeMessage
            )
            Result.success(Unit)
        } catch (e: GetCredentialException) {
            val authError = categorizeFirebaseError(e)
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = "Google Sign-In failed: ${authError.message}",
                errorSuggestion = authError.suggestion,
                successMessage = null
            )
            Result.failure(e)
        } catch (e: Exception) {
            val authError = categorizeFirebaseError(e)
            _authState.value = _authState.value.copy(
                isLoading = false,
                error = "Google Sign-In failed: ${authError.message}",
                errorSuggestion = authError.suggestion,
                successMessage = null
            )
            Result.failure(e)
        }
    }

    fun clearError() {
        _authState.value = _authState.value.copy(error = null, errorSuggestion = null, successMessage = null)
    }

    fun clearSuccessMessage() {
        _authState.value = _authState.value.copy(successMessage = null)
    }

    private fun mapFirebaseUserToUser(firebaseUser: FirebaseUser): User {
        return User(
            id = firebaseUser.uid,
            email = firebaseUser.email ?: "",
            displayName = firebaseUser.displayName,
            createdAt = firebaseUser.metadata?.creationTimestamp
        )
    }
}