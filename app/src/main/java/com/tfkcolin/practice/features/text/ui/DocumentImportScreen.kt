package com.tfkcolin.practice.features.text.ui

import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.material.icons.automirrored.filled.TextSnippet
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.lifecycle.viewmodel.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.practice.features.text.data.ProcessingStep
import com.tfkcolin.practice.features.text.domain.DocumentImportViewModel
import com.tfkcolin.practice.ui.components.ProfessionalButton
import com.tfkcolin.practice.ui.theme.*

private const val TAG = "DocumentImportScreen"

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DocumentImportScreen(
    modifier: Modifier = Modifier,
    onDocumentExtracted: (String, String?) -> Unit, // extracted text, source name
    onBackPressed: () -> Unit,
    viewModel: DocumentImportViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // Debug logging for UI state
    Log.d(TAG, "UI State - isLoading: ${uiState.isLoading}, extractedText: ${uiState.extractedText?.length ?: 0} chars, error: ${uiState.error}")
    Log.d(TAG, "UI State - processingStep: ${uiState.processingStep}, processedChunks: ${uiState.processedChunks}/${uiState.totalChunks}")

    // Document picker launcher
    val documentLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        Log.d(TAG, "Document launcher result received: $uri")
        uri?.let {
            Log.d(TAG, "Calling viewModel.onDocumentSelected with URI: $uri")
            viewModel.onDocumentSelected(it)
        } ?: Log.w(TAG, "Document launcher returned null URI")
    }


    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Import Document",
                        style = MaterialTheme.typography.titleLarge,
                        color = PrimaryBlack
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = PrimaryBlack
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = PrimaryWhite
                )
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(PrimaryWhite)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(32.dp))

            // Header text
            Text(
                text = "Select a document",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.SemiBold,
                color = PrimaryBlack,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "Choose a text file (.txt) or markdown file (.md) to extract and translate content",
                style = MaterialTheme.typography.bodyLarge,
                color = DarkGray,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 48.dp)
            )

            // Document selection area
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                colors = CardDefaults.cardColors(containerColor = LightGray),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.Description,
                        contentDescription = null,
                        tint = AccentGreen,
                        modifier = Modifier.size(80.dp)
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Supported formats",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = PrimaryBlack,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        FormatChip(text = ".txt", icon = Icons.AutoMirrored.Filled.TextSnippet)
                        FormatChip(text = ".md", icon = Icons.AutoMirrored.Filled.Article)
                    }
                }
            }

            // Loading state
            if (uiState.isLoading || uiState.processingStep != ProcessingStep.IDLE) {
                Log.d(TAG, "Showing loading/processing state - step: ${uiState.processingStep}, isLoading: ${uiState.isLoading}")
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = AccentBlue.copy(alpha = 0.1f))
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = AccentBlue,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                        val progressText = when (uiState.processingStep) {
                            ProcessingStep.DETECTING_LANGUAGE -> "Detecting language..."
                            ProcessingStep.PROCESSING_AI -> "Processing with AI..."
                            ProcessingStep.SAVING_RESULTS -> "Saving results..."
                            ProcessingStep.COMPLETED -> "Processing completed! Redirecting..."
                            ProcessingStep.ERROR -> "Error occurred"
                            else -> "Processing document..."
                        }
                        Text(
                            text = progressText,
                            style = MaterialTheme.typography.bodyMedium,
                            color = PrimaryBlack
                        )
                        if (uiState.totalChunks > 0) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "${uiState.processedChunks}/${uiState.totalChunks}",
                                style = MaterialTheme.typography.bodySmall,
                                color = DarkGray
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(24.dp))
            } else {
                Log.d(TAG, "Not showing loading state - step: ${uiState.processingStep}, isLoading: ${uiState.isLoading}")
            }

            // Error state
            uiState.error?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = ErrorRed.copy(alpha = 0.1f))
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = null,
                                tint = ErrorRed,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Import Failed",
                                style = MaterialTheme.typography.titleMedium,
                                color = ErrorRed,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = PrimaryBlack
                        )
                    }
                }
                Spacer(modifier = Modifier.height(24.dp))
            }

            // Action buttons
            if (uiState.extractedText == null) {
                Log.d(TAG, "Showing Select Document button - extractedText is null")
                ProfessionalButton(
                    text = "Select Document",
                    onClick = {
                        Log.d(TAG, "Select Document button clicked, launching document picker")
                        documentLauncher.launch("*/*") // Accept all files, filtering will be done in the service
                    },
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = AccentGreen
                )
            } else {
                Log.d(TAG, "Showing Process Document button - extractedText is not null, length: ${uiState.extractedText?.length}")
            }

            // Continue button when text is extracted
            uiState.extractedText?.let { extractedText ->
                Log.d(TAG, "Rendering Continue button")
                Spacer(modifier = Modifier.height(24.dp))

                ProfessionalButton(
                    text = "Continue to Validation",
                    onClick = {
                        Log.d(TAG, "Continue button clicked")
                        val sourceName = uiState.selectedDocumentUri?.lastPathSegment ?: "Unknown Document"
                        onDocumentExtracted(extractedText, sourceName)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = AccentBlue
                )
            } ?: Log.d(TAG, "Continue button not rendered - extractedText is null")

            Spacer(modifier = Modifier.weight(1f))

            // Info section
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = AccentGreen.copy(alpha = 0.1f))
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = AccentGreen,
                        modifier = Modifier.size(24.dp)
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Text(
                        text = "Large documents will be automatically split into manageable chunks for better translation quality.",
                        style = MaterialTheme.typography.bodySmall,
                        color = PrimaryBlack,
                        lineHeight = 18.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun FormatChip(
    text: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = PrimaryWhite),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = AccentGreen,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall,
                color = PrimaryBlack,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
