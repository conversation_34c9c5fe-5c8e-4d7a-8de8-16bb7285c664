package com.tfkcolin.practice.features.text.data

import android.graphics.Bitmap
import android.graphics.Matrix
import kotlin.math.max
import kotlin.math.min

/**
 * Utility class for processing captured images before OCR
 */
object ImageProcessingUtils {

    private const val MAX_IMAGE_SIZE = 2048 // Max dimension for OCR processing
    private const val TARGET_IMAGE_SIZE = 1024 // Target size for optimal OCR

    /**
     * Optimize bitmap for OCR processing
     * - Resize if too large
     * - Rotate if needed
     * - Convert to optimal format
     */
    fun optimizeBitmapForOCR(bitmap: Bitmap): Bitmap {
        var processedBitmap = bitmap

        try {
            // First, rotate if needed (most camera images are landscape)
            processedBitmap = rotateBitmapIfNeeded(processedBitmap)

            // Then resize if too large
            processedBitmap = resizeBitmapIfNeeded(processedBitmap)

            // Convert to RGB_565 for better OCR performance (optional)
            if (processedBitmap.config != Bitmap.Config.RGB_565) {
                val convertedBitmap = processedBitmap.copy(Bitmap.Config.RGB_565, false)
                if (convertedBitmap != null) {
                    // Only use converted bitmap if it was created successfully
                    if (processedBitmap != bitmap) {
                        processedBitmap.recycle()
                    }
                    processedBitmap = convertedBitmap
                }
            }

        } catch (e: Exception) {
            // If processing fails, return original bitmap
            return bitmap
        }

        return processedBitmap
    }

    /**
     * Rotate bitmap to portrait orientation if it's in landscape
     */
    private fun rotateBitmapIfNeeded(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height

        // If image is already taller than wide (portrait), no rotation needed
        if (height > width) {
            return bitmap
        }

        // Rotate 90 degrees clockwise for landscape images
        val matrix = Matrix()
        matrix.postRotate(90f)

        return Bitmap.createBitmap(
            bitmap,
            0,
            0,
            width,
            height,
            matrix,
            true
        )
    }

    /**
     * Resize bitmap if it's too large for efficient OCR processing
     */
    private fun resizeBitmapIfNeeded(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height

        // If both dimensions are within target size, no resize needed
        if (width <= TARGET_IMAGE_SIZE && height <= TARGET_IMAGE_SIZE) {
            return bitmap
        }

        // If image is extremely large, scale down to max size first
        val maxDimension = max(width, height)
        if (maxDimension > MAX_IMAGE_SIZE) {
            val scaleFactor = MAX_IMAGE_SIZE.toFloat() / maxDimension
            val newWidth = (width * scaleFactor).toInt()
            val newHeight = (height * scaleFactor).toInt()

            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            return scaledBitmap
        }

        // Scale down to target size while maintaining aspect ratio
        val scaleFactor = TARGET_IMAGE_SIZE.toFloat() / max(width, height)
        val newWidth = (width * scaleFactor).toInt()
        val newHeight = (height * scaleFactor).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * Check if bitmap is suitable for OCR processing
     */
    fun isBitmapSuitableForOCR(bitmap: Bitmap): Boolean {
        val width = bitmap.width
        val height = bitmap.height
        val minDimension = min(width, height)
        val maxDimension = max(width, height)

        // Image should not be too small or too large
        return minDimension >= 100 && maxDimension <= MAX_IMAGE_SIZE
    }

    /**
     * Get bitmap information for debugging
     */
    fun getBitmapInfo(bitmap: Bitmap): String {
        return "Bitmap(size=${bitmap.width}x${bitmap.height}, config=${bitmap.config}, " +
               "byteCount=${bitmap.byteCount}, density=${bitmap.density})"
    }
}