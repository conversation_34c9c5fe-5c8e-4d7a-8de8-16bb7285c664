package com.tfkcolin.practice.features.text.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.preferences.PreferencesRepository
import com.tfkcolin.practice.features.text.data.AITextProcessingService
import com.tfkcolin.practice.features.text.data.DocumentTextProcessingService
import com.tfkcolin.practice.features.text.data.TranslationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class TextProcessingUiState(
    val currentStep: ProcessingStep = ProcessingStep.IDLE,
    val progress: Float = 0f,
    val extractedText: String? = null,
    val detectedLanguage: String? = null,
    val translation: Translation? = null,
    val error: String? = null,
    val canCancel: Boolean = true
)

enum class ProcessingStep {
    IDLE,
    EXTRACTING_TEXT,
    DETECTING_LANGUAGE,
    SPLITTING_TEXT,
    PROCESSING_AI,
    SAVING_RESULTS,
    COMPLETED,
    ERROR
}

@HiltViewModel
class TextProcessingViewModel @Inject constructor(
    private val aiTextProcessingService: AITextProcessingService,
    private val documentTextProcessingService: DocumentTextProcessingService,
    private val translationRepository: TranslationRepository,
    private val preferencesRepository: PreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(TextProcessingUiState())
    val uiState: StateFlow<TextProcessingUiState> = _uiState.asStateFlow()

    fun startImageProcessing(extractedText: String, detectedLanguage: String?) {
        _uiState.value = TextProcessingUiState(
            currentStep = ProcessingStep.EXTRACTING_TEXT,
            progress = 0.1f,
            extractedText = extractedText,
            detectedLanguage = detectedLanguage
        )

        processText(extractedText, detectedLanguage, "image")
    }

    fun startDocumentProcessing(text: String, detectedLanguage: String?) {
        _uiState.value = TextProcessingUiState(
            currentStep = ProcessingStep.SPLITTING_TEXT,
            progress = 0.2f,
            extractedText = text,
            detectedLanguage = detectedLanguage
        )

        // Split text into chunks if needed
        val chunks = documentTextProcessingService.splitIntoParagraphs(text)

        if (chunks.isEmpty()) {
            _uiState.value = _uiState.value.copy(
                currentStep = ProcessingStep.ERROR,
                error = "No text content found to process"
            )
            return
        }

        processText(text, detectedLanguage, "document")
    }

    private fun processText(text: String, detectedLanguage: String?, source: String) {
        val userPrefs = preferencesRepository.getCurrentPreferences()
        val targetLanguage = userPrefs.selectedTargetLanguage
        val sourceLanguage = detectedLanguage ?: "auto" // Use detected or auto-detect

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    currentStep = ProcessingStep.PROCESSING_AI,
                    progress = 0.6f
                )

                aiTextProcessingService.processTextForTranslation(
                    text = text,
                    sourceLanguage = sourceLanguage,
                    targetLanguage = targetLanguage
                ) { result ->
                    result.fold(
                        onSuccess = { translation ->
                            saveTranslation(translation)
                        },
                        onFailure = { exception ->
                            _uiState.value = _uiState.value.copy(
                                currentStep = ProcessingStep.ERROR,
                                error = "AI processing failed: ${exception.localizedMessage}",
                                progress = 1f
                            )
                        }
                    )
                }

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    currentStep = ProcessingStep.ERROR,
                    error = "Processing failed: ${e.localizedMessage}",
                    progress = 1f
                )
            }
        }
    }

    private fun saveTranslation(translation: Translation) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    currentStep = ProcessingStep.SAVING_RESULTS,
                    progress = 0.8f
                )

                translationRepository.saveTranslation(translation)

                _uiState.value = _uiState.value.copy(
                    currentStep = ProcessingStep.COMPLETED,
                    progress = 1f,
                    translation = translation
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    currentStep = ProcessingStep.ERROR,
                    error = "Failed to save translation: ${e.localizedMessage}",
                    progress = 1f
                )
            }
        }
    }

    fun cancelProcessing() {
        if (_uiState.value.canCancel) {
            _uiState.value = TextProcessingUiState(
                currentStep = ProcessingStep.IDLE,
                error = "Processing cancelled by user"
            )
        }
    }

    fun retryProcessing() {
        val currentState = _uiState.value
        if (currentState.extractedText != null) {
            _uiState.value = TextProcessingUiState()
            when (currentState.extractedText) {
                currentState.extractedText -> {
                    startImageProcessing(currentState.extractedText!!, currentState.detectedLanguage)
                }
            }
        }
    }

    fun reset() {
        _uiState.value = TextProcessingUiState()
    }

    fun getProgressMessage(): String {
        return when (_uiState.value.currentStep) {
            ProcessingStep.IDLE -> "Ready to start"
            ProcessingStep.EXTRACTING_TEXT -> "Extracting text from image..."
            ProcessingStep.DETECTING_LANGUAGE -> "Detecting language..."
            ProcessingStep.SPLITTING_TEXT -> "Preparing text for processing..."
            ProcessingStep.PROCESSING_AI -> "Analyzing text with AI..."
            ProcessingStep.SAVING_RESULTS -> "Saving translation..."
            ProcessingStep.COMPLETED -> "Processing completed!"
            ProcessingStep.ERROR -> "Processing failed"
        }
    }
}