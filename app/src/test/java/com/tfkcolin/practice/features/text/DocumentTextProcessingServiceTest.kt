package com.tfkcolin.practice.features.text

import com.tfkcolin.practice.features.text.data.BasicDocumentTextProcessingService
import com.tfkcolin.practice.features.text.data.DocumentTextProcessingService
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

class DocumentTextProcessingServiceTest {

    private lateinit var service: DocumentTextProcessingService

    @Before
    fun setup() {
        service = BasicDocumentTextProcessingService()
    }

    @Test
    fun `MIME type validation - supported types`() {
        assertTrue(service.isSupportedMimeType("text/plain"))
        assertTrue(service.isSupportedMimeType("text/markdown"))
    }

    @Test
    fun `MIME type validation - unsupported types`() {
        assertFalse(service.isSupportedMimeType("application/pdf"))
        assertFalse(service.isSupportedMimeType("image/jpeg"))
        assertFalse(service.isSupportedMimeType(null))
        assertFalse(service.isSupportedMimeType(""))
    }

    @Test
    fun `Split empty text returns empty list`() {
        val result = service.splitIntoParagraphs("")
        assertTrue(result.isEmpty())

        val result2 = service.splitIntoParagraphs("   ")
        assertTrue(result2.isEmpty())
    }

    @Test
    fun `Split single paragraph within limit`() {
        val text = "This is a simple paragraph with some text."
        val chunks = service.splitIntoParagraphs(text, 100)

        assertEquals(1, chunks.size)
        assertEquals(text, chunks[0])
    }

    @Test
    fun `Split multiple paragraphs with context preservation`() {
        val text = """
            First paragraph with some content here.

            Second paragraph that should be separate.

            Third paragraph with more text to work with.
        """.trimIndent()

        val chunks = service.splitIntoParagraphs(text, 50)

        assertTrue(chunks.size >= 2)
        chunks.forEach { chunk ->
            assertTrue(chunk.length <= 150) // Should respect max length + overlap
        }
    }

    @Test
    fun `Split long text with sentence boundaries`() {
        val text = "This is sentence one. This is sentence two. This is sentence three. This is sentence four."

        val chunks = service.splitIntoParagraphs(text, 50)

        assertTrue(chunks.size >= 2)
        chunks.forEach { chunk ->
            assertTrue(chunk.length <= 150) // Max length + overlap (50 + 100)
        }

        // Verify sentences are kept together when possible
        val allText = chunks.joinToString(" ")
        assertTrue(allText.contains("sentence one"))
        assertTrue(allText.contains("sentence two"))
    }

    @Test
    fun `Context overlap between chunks`() {
        val text = "The quick brown fox jumps over the lazy dog. This is a second sentence that should provide context. Third sentence here for testing purposes."

        val chunks = service.splitIntoParagraphs(text, 50)

        // Check that we have multiple chunks
        assertTrue(chunks.size >= 2)

        // Check for overlap indicators
        val hasOverlap = chunks.any { it.contains("...") }
        // Note: Overlap might not always be present depending on exact splitting
    }

    @Test
    fun `Split very long sentence by words as fallback`() {
        val longSentence = "This is a very long sentence with many words that exceeds the maximum chunk length and should be split appropriately by word boundaries."
        val chunks = service.splitIntoParagraphs(longSentence, 30)

        assertTrue(chunks.size >= 3)
        chunks.forEach { chunk ->
            assertTrue(chunk.length <= 130) // Max length + overlap
        }

        // Verify words aren't cut in the middle
        chunks.forEach { chunk ->
            assertFalse(chunk.endsWith("ver")) // No word fragments
            assertFalse(chunk.endsWith("the")) // Complete words
        }
    }

    @Test
    fun `Handle text without sentence endings`() {
        val text = "This is a long text without proper sentence endings it just continues on and on"
        val chunks = service.splitIntoParagraphs(text, 40)

        assertTrue(chunks.size >= 2)
        chunks.forEach { chunk ->
            assertTrue(chunk.length <= 140) // Max length + overlap
        }
    }

    @Test
    fun `Preserve paragraph structure when possible`() {
        val text = """
            Introduction paragraph.

            Main content paragraph with important information.

            Conclusion paragraph.
        """.trimIndent()

        val chunks = service.splitIntoParagraphs(text, 100)

        // Should try to keep paragraphs together
        val completeParagraphs = chunks.count { chunk ->
            chunk.endsWith("paragraph.")
        }

        assertTrue(completeParagraphs >= 1)
    }

    @Test
    fun `Context preservation in overlapping chunks`() {
        val text1 = "First part of the story about technology."
        val text2 = "Second part continuing the technology discussion."
        val combinedText = "$text1 $text2"

        val chunks = service.splitIntoParagraphs(combinedText, text1.length + 10)

        if (chunks.size > 1) {
            // If split, check that context is preserved
            val secondChunk = chunks[1]
            assertTrue(secondChunk.contains("technology") ||
                      secondChunk.contains("story") ||
                      secondChunk.contains("..."))
        }
    }

    @Test
    fun `Handle mixed content with paragraphs and long sentences`() {
        val text = """
            Short paragraph.

            This is a very long sentence that contains many words and should be handled appropriately by the splitting algorithm when it exceeds the maximum chunk length for processing.

            Another short paragraph.
        """.trimIndent()

        val chunks = service.splitIntoParagraphs(text, 100)

        assertTrue(chunks.size >= 1)

        // Verify that the total content is preserved
        val allContent = chunks.joinToString(" ")
        assertTrue(allContent.contains("Short paragraph"))
        assertTrue(allContent.contains("Another short paragraph"))

        // Verify chunks respect size limits (with overlap)
        chunks.forEach { chunk ->
            assertTrue(chunk.length <= 200) // Max length + overlap
        }
    }

    @Test
    fun `Chunk size constraints are respected with overlap`() {
        val text = "This is a long text without proper sentence breaks. " + "A".repeat(1800) // Very long text with sentence start
        val chunks = service.splitIntoParagraphs(text, 150)

        chunks.forEach { chunk ->
            // Allow for overlap (150 + 100 = 250 max)
            assertTrue("Chunk length ${chunk.length} exceeds limit", chunk.length <= 250)
        }

        // Should create multiple chunks due to length
        assertTrue("Expected multiple chunks, got ${chunks.size}", chunks.size >= 2)
        assertTrue("Too many chunks: ${chunks.size}", chunks.size <= 20)
    }
}