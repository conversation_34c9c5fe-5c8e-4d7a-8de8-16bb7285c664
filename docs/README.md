# Language Practice App Documentation

## Overview

The Language Practice App is an Android application designed to help users improve their language proficiency through various interactive practice modes. The app leverages AI-powered text processing, speech recognition, text-to-speech, and computer vision to create a comprehensive language learning experience.

## Table of Contents

- [App Purpose](#app-purpose)
- [Architecture Overview](#architecture-overview)
- [Key Features](#key-features)
- [Getting Started](#getting-started)
- [Development Guidelines](#development-guidelines)
- [Documentation Structure](#documentation-structure)

## App Purpose

The app serves as a comprehensive language learning platform that allows users to:

1. **Import Content**: Extract text from various sources (images, documents, audio files)
2. **Practice Speaking**: Use TTS and STT engines for pronunciation practice
3. **Interactive Learning**: Engage with AI-powered conversation practice
4. **Track Progress**: Monitor learning progress through frequency statistics
5. **Multi-modal Input**: Support text, audio, image, and document inputs

## Architecture Overview

The app follows **Clean Architecture** principles with **MVVM pattern** and uses:

- **Jetpack Compose** for modern UI
- **Hilt** for dependency injection
- **Room** for local database
- **Firebase** for authentication and AI services
- **ML Kit** for text recognition and language detection
- **CameraX** for camera functionality

### Core Components

- **Features**: Modular feature-based architecture
- **Data Layer**: Repositories, databases, and engines
- **Domain Layer**: ViewModels and business logic
- **UI Layer**: Compose screens and components

## Key Features

### 1. Authentication System
- Email/password authentication
- Google Sign-In integration
- Firebase Auth backend

### 2. Content Input System
- **Text Import**: Direct text input and document import
- **Image Processing**: Camera capture and gallery selection with OCR
- **Audio Processing**: Audio file import and transcription
- **Live Text Recognition**: Real-time camera text detection

### 3. Language Processing
- **AI Text Processing**: Firebase Gemini integration for translation and analysis
- **Language Detection**: Automatic source language detection
- **Translation**: Multi-language translation support
- **Word/Expression Analysis**: Detailed breakdown with pronunciations

### 4. Speech & Audio System
- **TTS Engines**: Android TTS and Google Cloud TTS (planned)
- **STT Engines**: Android Speech Recognition
- **Language Manager**: Coordinated TTS/STT language selection
- **Audio Transcription**: Efficient audio processing pipeline

### 5. Practice Modes
- **Listening & Speaking Practice**: Pronunciation and comprehension
- **AI Conversation**: Interactive dialogue with AI tutor
- **Content-based Practice**: Practice with imported materials

### 6. Data Management
- **Translation Storage**: Room database for translations
- **Frequency Tracking**: Word and expression encounter statistics
- **User Preferences**: Language settings and configurations

## Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24+ (Android 7.0)
- Firebase project setup
- Google Cloud credentials (for advanced features)

### Setup Instructions
1. Clone the repository
2. Open in Android Studio
3. Add `google-services.json` to `app/` directory
4. Configure Firebase Authentication
5. Build and run the project

## Development Guidelines

### Code Organization
- Follow feature-based modular architecture
- Use dependency injection with Hilt
- Implement proper error handling
- Write unit tests for business logic

### UI Development
- Use Jetpack Compose for all UI
- Follow Material Design 3 guidelines
- Implement proper accessibility features
- Use consistent theming and colors

### Data Management
- Use Room for local data persistence
- Implement proper data validation
- Handle offline scenarios gracefully
- Use coroutines for asynchronous operations

## Documentation Structure

- [`architecture.md`](./architecture.md) - Detailed architecture documentation
- [`features/`](./features/) - Feature-specific documentation
- [`api/`](./api/) - API and service documentation
- [`development/`](./development/) - Development setup and guidelines
- [`practice-screens.md`](./practice-screens.md) - Practice screen specifications
- [`deployment.md`](./deployment/) - Deployment and release guidelines

## Contributing

Please read the development guidelines and follow the established patterns when contributing to the project. All new features should include proper documentation and tests.

## License

[Add license information here]
