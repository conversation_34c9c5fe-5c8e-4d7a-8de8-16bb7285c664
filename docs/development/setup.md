# Development Setup Guide

This guide will help new team members set up their development environment for the Language Practice App.

## Prerequisites

### Required Software
- **Android Studio**: Arctic Fox (2020.3.1) or later
- **JDK**: Java 11 or later
- **Git**: Latest version
- **Android SDK**: API level 24+ (Android 7.0)

### Recommended Tools
- **Gradle**: 7.0+ (bundled with Android Studio)
- **Kotlin**: 1.8+ (bundled with Android Studio)
- **Firebase CLI**: For Firebase operations

## Project Setup

### 1. Clone Repository
```bash
git clone [repository-url]
cd practice
```

### 2. Android Studio Configuration

#### Import Project
1. Open Android Studio
2. Select "Open an existing Android Studio project"
3. Navigate to the cloned repository
4. Wait for Gradle sync to complete

#### SDK Configuration
1. Go to `File > Project Structure > SDK Location`
2. Ensure Android SDK path is correct
3. Verify SDK platforms are installed:
   - Android 7.0 (API 24) - Minimum
   - Android 14 (API 34) - Target
   - Latest available version

### 3. Firebase Setup

#### Prerequisites
- Firebase project access
- Google account with project permissions

#### Configuration Steps
1. **Download Configuration File**
   - Go to Firebase Console
   - Select the project
   - Download `google-services.json`
   - Place in `app/` directory

2. **Verify Firebase Services**
   - Authentication: Email/Password and Google Sign-In
   - Firestore: Database for user data
   - Firebase AI: Gemini integration
   - App Check: Security verification

#### Environment Variables
Create `local.properties` file in project root:
```properties
# Firebase
firebase.project.id=your-project-id
firebase.web.client.id=your-web-client-id

# Google Cloud (if using)
google.cloud.api.key=your-api-key
```

### 4. Dependencies and Build

#### Gradle Sync
1. Open `build.gradle.kts` files
2. Click "Sync Now" when prompted
3. Wait for dependency resolution

#### Build Variants
- **Debug**: Development builds with debugging enabled
- **Release**: Production builds with optimizations

#### Key Dependencies
```kotlin
// Core Android
implementation("androidx.core:core-ktx:1.12.0")
implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
implementation("androidx.activity:activity-compose:1.8.2")

// Compose
implementation(platform("androidx.compose:compose-bom:2023.10.01"))
implementation("androidx.compose.ui:ui")
implementation("androidx.compose.material3:material3")

// Navigation
implementation("androidx.navigation:navigation-compose:2.7.5")
implementation("androidx.hilt:hilt-navigation-compose:1.1.0")

// Dependency Injection
implementation("com.google.dagger:hilt-android:2.48")
kapt("com.google.dagger:hilt-compiler:2.48")

// Database
implementation("androidx.room:room-runtime:2.6.1")
implementation("androidx.room:room-ktx:2.6.1")
kapt("androidx.room:room-compiler:2.6.1")

// Firebase
implementation("com.google.firebase:firebase-auth:22.3.0")
implementation("com.google.firebase:firebase-firestore:24.9.1")
implementation("com.google.firebase:firebase-ai:0.1.0")

// ML Kit
implementation("com.google.mlkit:text-recognition:16.0.0")
implementation("com.google.mlkit:language-id:17.0.4")

// CameraX
implementation("androidx.camera:camera-core:1.3.1")
implementation("androidx.camera:camera-camera2:1.3.1")
implementation("androidx.camera:camera-lifecycle:1.3.1")
implementation("androidx.camera:camera-view:1.3.1")
```

## Development Environment

### 1. Code Style and Formatting

#### Kotlin Code Style
- Follow official Kotlin coding conventions
- Use Android Studio's built-in formatter
- Configure auto-formatting on save

#### Import Organization
```kotlin
// Android imports
import android.content.Context
import androidx.compose.runtime.*

// Third-party imports
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.lifecycle.HiltViewModel

// Project imports
import com.tfkcolin.practice.data.model.Translation
```

### 2. Git Configuration

#### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: Feature development
- `bugfix/*`: Bug fixes
- `hotfix/*`: Critical fixes

#### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Example:
```
feat(auth): add Google Sign-In integration

Implement Google Sign-In using Credential Manager API
- Add Google ID token validation
- Update AuthRepository with new sign-in method
- Add error handling for Google Sign-In failures

Closes #123
```

### 3. Testing Setup

#### Unit Testing
```kotlin
// Test dependencies
testImplementation("junit:junit:4.13.2")
testImplementation("org.mockito:mockito-core:5.7.0")
testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")
testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.1")
```

#### UI Testing
```kotlin
// Android test dependencies
androidTestImplementation("androidx.test.ext:junit:1.1.5")
androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
androidTestImplementation("androidx.compose.ui:ui-test-junit4")
```

#### Running Tests
```bash
# Unit tests
./gradlew test

# Instrumented tests
./gradlew connectedAndroidTest

# Specific test class
./gradlew test --tests "com.tfkcolin.practice.features.auth.AuthViewModelTest"
```

### 4. Debugging Setup

#### Debug Configuration
1. **Build Variants**: Select `debug` variant
2. **Debugging**: Enable USB debugging on device
3. **Logging**: Use structured logging with tags

#### Logging Best Practices
```kotlin
companion object {
    private const val TAG = "FeatureName"
}

Log.d(TAG, "Debug message")
Log.i(TAG, "Info message")
Log.w(TAG, "Warning message")
Log.e(TAG, "Error message", exception)
```

#### Firebase Debug View
- Enable debug mode for Firebase Analytics
- Use Firebase Console for real-time debugging
- Monitor Crashlytics for error tracking

## IDE Configuration

### 1. Android Studio Plugins

#### Recommended Plugins
- **Kotlin**: Built-in Kotlin support
- **Hilt**: Dependency injection support
- **Room**: Database inspection
- **Firebase**: Firebase integration tools

#### Optional Plugins
- **GitToolBox**: Enhanced Git integration
- **Rainbow Brackets**: Code readability
- **Key Promoter X**: Keyboard shortcut learning

### 2. Code Templates

#### Live Templates
Create custom live templates for common patterns:

**ViewModel Template**
```kotlin
@HiltViewModel
class ${NAME}ViewModel @Inject constructor(
    private val repository: ${REPOSITORY}
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(${STATE}())
    val uiState: StateFlow<${STATE}> = _uiState.asStateFlow()
    
    $END$
}
```

**Compose Screen Template**
```kotlin
@Composable
fun ${NAME}Screen(
    modifier: Modifier = Modifier,
    viewModel: ${NAME}ViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    $END$
}
```

### 3. Build Configuration

#### Gradle Properties
Add to `gradle.properties`:
```properties
# Performance optimizations
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Kotlin compiler optimizations
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true
```

#### Build Optimization
- Enable build cache
- Use parallel builds
- Configure proper heap sizes
- Enable incremental compilation

## Troubleshooting

### Common Issues

#### 1. Gradle Sync Failures
**Problem**: Dependencies not resolving
**Solution**: 
- Clear Gradle cache: `./gradlew clean`
- Invalidate caches: `File > Invalidate Caches and Restart`
- Check internet connection and proxy settings

#### 2. Firebase Configuration
**Problem**: Firebase services not working
**Solution**:
- Verify `google-services.json` is in correct location
- Check Firebase project configuration
- Ensure all required services are enabled

#### 3. Build Errors
**Problem**: Compilation failures
**Solution**:
- Check Kotlin version compatibility
- Verify all dependencies are compatible
- Clean and rebuild project

#### 4. Device Connection Issues
**Problem**: Device not detected
**Solution**:
- Enable USB debugging
- Install proper USB drivers
- Check USB connection mode

### Getting Help

#### Internal Resources
- Team documentation in `docs/` directory
- Code comments and inline documentation
- Git commit history for context

#### External Resources
- [Android Developer Documentation](https://developer.android.com/)
- [Jetpack Compose Documentation](https://developer.android.com/jetpack/compose)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Kotlin Documentation](https://kotlinlang.org/docs/)

#### Team Communication
- Create GitHub issues for bugs
- Use pull requests for code reviews
- Document decisions in commit messages
- Update documentation with changes

## Next Steps

After completing setup:
1. **Read Architecture Documentation**: Understand app structure
2. **Explore Feature Documentation**: Learn about specific features
3. **Run Sample Tests**: Verify everything works
4. **Make First Contribution**: Start with small improvements
5. **Join Team Discussions**: Participate in planning and reviews
