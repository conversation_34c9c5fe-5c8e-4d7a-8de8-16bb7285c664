# Adding TTS/STT Providers Guide

This guide explains how to add new Text-to-Speech (TTS) and Speech-to-Text (STT) providers to the Language Practice App.

## Overview

The app uses a modular engine system that allows multiple TTS and STT providers to be integrated seamlessly. The system is designed for easy extensibility and provider switching.

## Architecture

### Engine System Components

1. **Engine Interfaces**: Define contracts for TTS and STT functionality
2. **Engine Implementations**: Concrete implementations for specific providers
3. **Repositories**: Manage engine selection and coordination
4. **Language Manager**: Coordinates TTS/STT engines with language selection

### Current Providers

**TTS Providers:**
- `AndroidTtsEngine`: System TTS (fully implemented)
- `GoogleCloudTtsEngine`: Google Cloud TTS (placeholder)

**STT Providers:**
- `AndroidSpeechEngine`: System Speech Recognition (fully implemented)

## Adding a New TTS Provider

### Step 1: Implement TTS Engine Interface

Create a new engine class implementing `TtsEngine`:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/data/engine/YourTtsEngine.kt

class YourTtsEngine(
    private val context: Application,
    private val apiKey: String? = null
) : TtsEngine {
    
    private var isInitialized = false
    private var currentLocale: Locale = Locale.getDefault()
    
    override fun init(callback: (Boolean) -> Unit) {
        // Initialize your TTS provider
        // This might involve:
        // - Setting up API clients
        // - Authenticating with services
        // - Loading necessary resources
        
        try {
            // Your initialization code here
            isInitialized = true
            callback(true)
        } catch (e: Exception) {
            isInitialized = false
            callback(false)
        }
    }
    
    override fun speak(text: String) {
        if (!isInitialized) return
        
        // Implement text-to-speech functionality
        // This might involve:
        // - Making API calls to cloud services
        // - Using local TTS libraries
        // - Managing audio playback
    }
    
    override fun setLanguage(locale: Locale) {
        currentLocale = locale
        // Update provider language settings
    }
    
    override fun getAvailableLanguages(): List<Locale> {
        // Return list of supported languages
        return listOf(
            Locale.ENGLISH,
            Locale.FRENCH,
            Locale.GERMAN,
            // Add supported languages
        )
    }
    
    override fun shutdown() {
        // Clean up resources
        // - Close connections
        // - Release audio resources
        // - Cancel ongoing operations
        isInitialized = false
    }
    
    override val displayName: String = "Your TTS Provider"
}
```

### Step 2: Register Engine in Dependency Injection

Update `AppModule.kt` to include your new engine:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/di/AppModule.kt

@Module
@InstallIn(SingletonComponent::class)
object TtsModule {
    @Provides
    @Singleton
    fun provideTtsRepository(
        @ApplicationContext context: Context,
    ): TtsRepository {
        val engines = mapOf(
            "android" to AndroidTtsEngine(context as Application),
            "google" to GoogleCloudTtsEngine(),
            "your_provider" to YourTtsEngine(context as Application, apiKey = "your-api-key")
        )
        return TtsRepository(engines)
    }
}
```

### Step 3: Update Engine Selection UI

Add your provider to the engine selector:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/core/ui/EngineSelector.kt

@Composable
fun EngineSelector(viewModel: TtsViewModel, uiState: TtsUiState) {
    val engines = mapOf(
        "android" to "System (Android)",
        "google" to "Google Cloud",
        "your_provider" to "Your TTS Provider"
    )
    
    // Rest of the implementation remains the same
}
```

### Step 4: Update Language Manager

If your provider has specific language preferences, update the `LanguageManager`:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/features/language/domain/LanguageManager.kt

private val enginePreferences = mapOf(
    // Existing preferences
    "en" to Pair("android", "android"),
    "es" to Pair("android", "android"),
    "de" to Pair("google", "android"),
    "fr" to Pair("your_provider", "android"), // Prefer your provider for French
    // Add more language preferences
)
```

## Adding a New STT Provider

### Step 1: Implement STT Engine Interface

Create a new engine class implementing `SpeechEngine`:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/data/engine/YourSpeechEngine.kt

class YourSpeechEngine(
    private val context: Application,
    private val apiKey: String? = null
) : SpeechEngine {
    
    private var isInitialized = false
    private var currentLocale: Locale = Locale.getDefault()
    private var _isListening = false
    private var recognitionCallback: ((String) -> Unit)? = null
    private var errorCallback: ((String) -> Unit)? = null
    
    override val isListening: Boolean get() = _isListening
    
    override fun init(callback: (Boolean) -> Unit) {
        try {
            // Initialize your STT provider
            // - Set up API clients
            // - Configure audio recording
            // - Authenticate with services
            
            isInitialized = true
            callback(true)
        } catch (e: Exception) {
            isInitialized = false
            callback(false)
        }
    }
    
    override fun startListening(
        callback: (String) -> Unit, 
        errorCallback: (String) -> Unit
    ) {
        if (!isInitialized) {
            errorCallback("Engine not initialized")
            return
        }
        
        this.recognitionCallback = callback
        this.errorCallback = errorCallback
        _isListening = true
        
        // Start speech recognition
        // This might involve:
        // - Starting audio recording
        // - Streaming audio to cloud services
        // - Processing real-time results
    }
    
    override fun stopListening() {
        _isListening = false
        // Stop recognition and clean up
    }
    
    override fun setLanguage(locale: Locale) {
        currentLocale = locale
        // Update provider language settings
    }
    
    override fun getAvailableLanguages(): List<Locale> {
        return listOf(
            Locale.ENGLISH,
            Locale.FRENCH,
            Locale.GERMAN,
            // Add supported languages
        )
    }
    
    override fun shutdown() {
        stopListening()
        // Clean up resources
        isInitialized = false
    }
    
    override val displayName: String = "Your Speech Provider"
}
```

### Step 2: Register STT Engine

Update the STT module in `AppModule.kt`:

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {
    @Provides
    @Singleton
    fun provideSpeechRepository(
        @ApplicationContext context: Context,
    ): SpeechRepository {
        val engines = mapOf(
            "android" to AndroidSpeechEngine(context as Application),
            "your_provider" to YourSpeechEngine(context as Application, apiKey = "your-api-key")
        )
        return SpeechRepository(engines)
    }
}
```

## Provider-Specific Considerations

### Cloud-Based Providers

For cloud-based TTS/STT providers:

1. **API Key Management**
   ```kotlin
   // Store API keys securely
   private val apiKey = BuildConfig.YOUR_PROVIDER_API_KEY
   
   // Or use encrypted preferences
   private val encryptedPrefs = EncryptedSharedPreferences.create(...)
   ```

2. **Network Handling**
   ```kotlin
   // Implement proper error handling
   try {
       val response = apiClient.synthesize(text, language)
       // Handle response
   } catch (e: NetworkException) {
       // Handle network errors
   } catch (e: ApiException) {
       // Handle API errors
   }
   ```

3. **Caching Strategy**
   ```kotlin
   // Cache frequently used audio
   private val audioCache = LruCache<String, ByteArray>(maxSize)
   
   override fun speak(text: String) {
       val cacheKey = "${text}_${currentLocale}"
       val cachedAudio = audioCache.get(cacheKey)
       
       if (cachedAudio != null) {
           playAudio(cachedAudio)
       } else {
           synthesizeAndCache(text, cacheKey)
       }
   }
   ```

### Local Providers

For local/offline providers:

1. **Resource Management**
   ```kotlin
   // Load models and resources efficiently
   private fun loadLanguageModel(locale: Locale) {
       val modelPath = "models/${locale.language}.bin"
       // Load from assets or download if needed
   }
   ```

2. **Performance Optimization**
   ```kotlin
   // Use background threads for heavy operations
   private val processingScope = CoroutineScope(Dispatchers.IO)
   
   override fun speak(text: String) {
       processingScope.launch {
           val audio = synthesizeAudio(text)
           withContext(Dispatchers.Main) {
               playAudio(audio)
           }
       }
   }
   ```

## Testing New Providers

### Unit Testing

Create comprehensive tests for your engine:

```kotlin
// File: app/src/test/java/com/tfkcolin/practice/data/engine/YourTtsEngineTest.kt

class YourTtsEngineTest {
    
    private lateinit var engine: YourTtsEngine
    private lateinit var mockContext: Application
    
    @Before
    fun setup() {
        mockContext = mock()
        engine = YourTtsEngine(mockContext, "test-api-key")
    }
    
    @Test
    fun `init should succeed with valid configuration`() {
        var initResult = false
        engine.init { success -> initResult = success }
        
        assertTrue(initResult)
    }
    
    @Test
    fun `speak should handle text correctly`() {
        engine.init { }
        engine.speak("Hello, world!")
        
        // Verify speech synthesis
    }
    
    @Test
    fun `getAvailableLanguages should return supported languages`() {
        val languages = engine.getAvailableLanguages()
        
        assertFalse(languages.isEmpty())
        assertTrue(languages.contains(Locale.ENGLISH))
    }
}
```

### Integration Testing

Test engine integration with the repository:

```kotlin
@Test
fun `repository should switch to new engine successfully`() {
    val repository = TtsRepository(engines)
    
    repository.chooseEngine("your_provider") { success ->
        assertTrue(success)
        
        // Test engine functionality
        repository.speak("Test text")
        val languages = repository.getAvailableLanguages()
        assertFalse(languages.isEmpty())
    }
}
```

## Configuration and Settings

### Engine Configuration

Add configuration options for your provider:

```kotlin
// File: app/src/main/java/com/tfkcolin/practice/data/preferences/EnginePreferences.kt

data class EngineConfiguration(
    val apiKey: String? = null,
    val serverUrl: String? = null,
    val quality: AudioQuality = AudioQuality.STANDARD,
    val cacheEnabled: Boolean = true
)

enum class AudioQuality {
    LOW, STANDARD, HIGH, PREMIUM
}
```

### Settings UI

Add provider-specific settings to the settings screen:

```kotlin
// Add to SettingsScreen.kt
item {
    if (uiState.selectedTtsEngine == "your_provider") {
        YourProviderSettings(
            configuration = uiState.providerConfiguration,
            onConfigurationChange = viewModel::updateProviderConfiguration
        )
    }
}

@Composable
fun YourProviderSettings(
    configuration: EngineConfiguration,
    onConfigurationChange: (EngineConfiguration) -> Unit
) {
    Column {
        Text("Your Provider Settings")
        
        // Quality selection
        QualitySelector(
            selectedQuality = configuration.quality,
            onQualityChange = { quality ->
                onConfigurationChange(configuration.copy(quality = quality))
            }
        )
        
        // Cache toggle
        Switch(
            checked = configuration.cacheEnabled,
            onCheckedChange = { enabled ->
                onConfigurationChange(configuration.copy(cacheEnabled = enabled))
            }
        )
    }
}
```

## Best Practices

### Error Handling
- Implement comprehensive error handling
- Provide meaningful error messages to users
- Handle network failures gracefully
- Implement retry mechanisms where appropriate

### Performance
- Use background threads for heavy operations
- Implement proper caching strategies
- Optimize memory usage
- Handle large audio files efficiently

### User Experience
- Provide clear feedback during initialization
- Show loading states for network operations
- Allow users to cancel long-running operations
- Implement proper offline fallbacks

### Security
- Store API keys securely
- Validate all inputs
- Use secure communication protocols
- Implement proper authentication

## Troubleshooting

### Common Issues

1. **Initialization Failures**
   - Check API key validity
   - Verify network connectivity
   - Ensure proper permissions

2. **Audio Playback Issues**
   - Check audio focus management
   - Verify audio format compatibility
   - Handle audio routing changes

3. **Language Support**
   - Verify language codes match provider expectations
   - Check locale formatting
   - Handle unsupported languages gracefully

### Debugging Tips

- Use detailed logging for troubleshooting
- Implement debug modes for testing
- Monitor network requests and responses
- Test with various device configurations

## Documentation Requirements

When adding a new provider:

1. **Update this guide** with provider-specific information
2. **Add API documentation** for any new interfaces
3. **Update feature documentation** to reflect new capabilities
4. **Create user documentation** for new settings and features
5. **Add troubleshooting information** for common issues
