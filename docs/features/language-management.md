# Language Management Feature Documentation

## Overview

The Language Management feature coordinates Text-to-Speech (TTS) and Speech-to-Text (STT) engines with language selection, providing a unified interface for language configuration across the app.

## Architecture

### Core Components

1. **LanguageManager**: Central coordinator for language and engine management
2. **TtsRepository**: Manages TTS engine selection and operations
3. **SpeechRepository**: Manages STT engine selection and operations
4. **Engine Implementations**: Concrete TTS/STT engine implementations
5. **PreferencesRepository**: Stores user language preferences

### Component Relationships

```
LanguageManager
├── TtsRepository
│   ├── AndroidTtsEngine
│   └── GoogleCloudTtsEngine (planned)
├── SpeechRepository
│   └── AndroidSpeechEngine
└── PreferencesRepository
```

## Language Manager

### Core Functionality

**Responsibilities:**
- Coordinate TTS and STT engine selection
- Manage language preferences
- Ensure engine compatibility
- Handle initialization and error states

**Implementation:**
```kotlin
@Singleton
class LanguageManager @Inject constructor(
    private val ttsRepository: TtsRepository,
    private val speechRepository: SpeechRepository,
    private val preferencesRepository: PreferencesRepository
) {
    private val _state = MutableStateFlow(LanguageManagerState())
    val state: StateFlow<LanguageManagerState> = _state.asStateFlow()
    
    suspend fun initializeLanguages() {
        // Initialize available languages and engines
    }
    
    suspend fun selectLanguage(language: SupportedLanguage) {
        // Configure engines for selected language
    }
}
```

### State Management

```kotlin
data class LanguageManagerState(
    val supportedLanguages: List<SupportedLanguage> = emptyList(),
    val selectedLanguage: SupportedLanguage? = null,
    val isInitializing: Boolean = false,
    val isReady: Boolean = false,
    val error: String? = null
)

data class SupportedLanguage(
    val languageCode: String,
    val countryCode: String?,
    val displayName: String,
    val ttsEngineKey: String,
    val speechEngineKey: String,
    val locale: Locale
)
```

## Engine System

### TTS Engine Interface

```kotlin
interface TtsEngine {
    fun init(callback: (Boolean) -> Unit)
    fun shutdown()
    fun speak(text: String)
    fun setLanguage(locale: Locale)
    fun getAvailableLanguages(): List<Locale>
    val displayName: String
}
```

### STT Engine Interface

```kotlin
interface SpeechEngine {
    fun init(callback: (Boolean) -> Unit)
    fun shutdown()
    fun startListening(callback: (String) -> Unit, errorCallback: (String) -> Unit)
    fun stopListening()
    fun setLanguage(locale: Locale)
    fun getAvailableLanguages(): List<Locale>
    val displayName: String
    val isListening: Boolean
}
```

### Current Engine Implementations

#### Android TTS Engine
```kotlin
class AndroidTtsEngine(private val context: Application) : TtsEngine {
    private var tts: TextToSpeech? = null
    private var currentLocale: Locale = Locale.getDefault()
    
    override fun init(callback: (Boolean) -> Unit) {
        tts = TextToSpeech(context) { status ->
            val ok = status == TextToSpeech.SUCCESS
            if (ok) tts?.language = currentLocale
            callback(ok)
        }
    }
    
    override fun speak(text: String) {
        tts?.speak(text, TextToSpeech.QUEUE_FLUSH, null, null)
    }
    
    // Additional implementation details...
}
```

#### Android Speech Engine
```kotlin
class AndroidSpeechEngine(private val context: Application) : SpeechEngine {
    private var speechRecognizer: SpeechRecognizer? = null
    private var currentLocale: Locale = Locale.getDefault()
    private var _isListening = false
    
    override fun startListening(callback: (String) -> Unit, errorCallback: (String) -> Unit) {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, currentLocale.toLanguageTag())
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }
        
        _isListening = true
        speechRecognizer?.startListening(intent)
    }
    
    // Additional implementation details...
}
```

## Language Selection Process

### Initialization Flow

1. **Engine Discovery**: Query available TTS and STT engines
2. **Language Enumeration**: Get supported languages from each engine
3. **Compatibility Matching**: Find languages supported by both TTS and STT
4. **Preference Loading**: Load user's previously selected language
5. **Engine Configuration**: Initialize engines with selected language

### Language Selection Flow

1. **User Selection**: User chooses a language from available options
2. **Engine Lookup**: Determine preferred engines for the language
3. **TTS Configuration**: Initialize and configure TTS engine
4. **STT Configuration**: Initialize and configure STT engine
5. **Validation**: Verify both engines are ready
6. **Preference Storage**: Save selection for future sessions
7. **State Update**: Update UI state with new configuration

## Engine Preferences

### Language-Engine Mapping

```kotlin
private val enginePreferences = mapOf(
    // English - prefer Android TTS and Speech
    "en" to Pair("android", "android"),
    // Spanish - prefer Android TTS and Speech
    "es" to Pair("android", "android"),
    // German - prefer Google TTS if available, Android Speech
    "de" to Pair("google", "android"),
    // French - prefer Google TTS if available, Android Speech
    "fr" to Pair("google", "android"),
    // Japanese - prefer Google TTS if available, Android Speech
    "ja" to Pair("google", "android"),
    // Chinese - prefer Google TTS if available, Android Speech
    "zh" to Pair("google", "android")
)
```

### Fallback Strategy

1. **Primary Engine**: Try preferred engine for language
2. **Secondary Engine**: Fall back to Android system engines
3. **Default Language**: Fall back to system default language
4. **Error Handling**: Graceful degradation with user notification

## Repository Pattern

### TTS Repository

```kotlin
class TtsRepository(private val engines: Map<String, TtsEngine>) {
    private var currentEngine: TtsEngine? = null
    
    fun chooseEngine(key: String, callback: (Boolean) -> Unit) {
        currentEngine?.shutdown()
        currentEngine = engines[key]
        currentEngine?.init { success ->
            if (!success) currentEngine = null
            callback(success)
        } ?: callback(false)
    }
    
    fun speak(text: String) = currentEngine?.speak(text)
    fun setLanguage(locale: Locale) = currentEngine?.setLanguage(locale)
    fun getAvailableLanguages(): List<Locale> = currentEngine?.getAvailableLanguages() ?: emptyList()
}
```

### Speech Repository

```kotlin
class SpeechRepository(private val engines: Map<String, SpeechEngine>) {
    private var currentEngine: SpeechEngine? = null
    
    fun chooseEngine(key: String, callback: (Boolean) -> Unit) {
        currentEngine?.shutdown()
        currentEngine = engines[key]
        currentEngine?.init { success ->
            if (!success) currentEngine = null
            callback(success)
        } ?: callback(false)
    }
    
    fun startListening(callback: (String) -> Unit, errorCallback: (String) -> Unit) {
        currentEngine?.startListening(callback, errorCallback)
    }
    
    val isListening: Boolean get() = currentEngine?.isListening ?: false
}
```

## User Preferences

### Preference Storage

```kotlin
data class UserPreferences(
    val selectedLanguageCode: String? = null,
    val selectedLanguageCountry: String? = null,
    val selectedTargetLanguage: String = "en",
    val hasCompletedLanguageSelection: Boolean = false
)

class PreferencesRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    fun saveSelectedLanguage(language: SupportedLanguage) {
        val preferences = UserPreferences.fromSupportedLanguage(language, hasCompletedSelection = true)
        saveUserPreferences(preferences)
    }
    
    fun getCurrentPreferences(): UserPreferences {
        return _userPreferences.value
    }
}
```

### Settings Integration

The language management integrates with the Settings screen to provide:
- Language selection interface
- Engine status display
- Error handling and retry options
- Target language configuration

## Error Handling

### Common Error Scenarios

1. **Engine Initialization Failure**
   - Network connectivity issues (for cloud engines)
   - Missing system components
   - Insufficient permissions

2. **Language Not Supported**
   - Engine doesn't support selected language
   - Partial support (TTS only or STT only)

3. **Runtime Errors**
   - Engine crashes or becomes unresponsive
   - Audio system conflicts
   - Resource exhaustion

### Error Recovery Strategies

```kotlin
private suspend fun handleEngineError(error: EngineError) {
    when (error) {
        is EngineError.InitializationFailed -> {
            // Try fallback engine
            tryFallbackEngine(error.engineKey)
        }
        is EngineError.LanguageNotSupported -> {
            // Suggest alternative languages
            suggestAlternativeLanguages(error.requestedLanguage)
        }
        is EngineError.RuntimeError -> {
            // Restart engine
            restartEngine(error.engineKey)
        }
    }
}
```

## Performance Considerations

### Initialization Optimization
- Lazy engine initialization
- Background engine preparation
- Cached language availability
- Parallel engine setup

### Memory Management
- Proper engine lifecycle management
- Resource cleanup on language changes
- Audio buffer management
- Cache size limits

### Network Optimization (for cloud engines)
- Connection pooling
- Request batching
- Offline fallbacks
- Bandwidth adaptation

## Testing Strategy

### Unit Tests
```kotlin
class LanguageManagerTest {
    @Test
    fun `selectLanguage should configure both engines successfully`() {
        // Test language selection flow
    }
    
    @Test
    fun `should handle engine initialization failure gracefully`() {
        // Test error handling
    }
}
```

### Integration Tests
- Engine compatibility testing
- Language switching scenarios
- Error recovery testing
- Performance benchmarking

## Future Enhancements

### Additional Engine Support
- Google Cloud TTS integration
- Azure Cognitive Services
- Amazon Polly
- Custom voice models

### Advanced Features
- Voice cloning capabilities
- Accent selection
- Speaking rate adjustment
- Pitch and tone control

### Smart Language Detection
- Automatic language switching
- Mixed-language content handling
- Context-aware language selection
- User behavior learning

### Offline Capabilities
- Downloadable language packs
- Offline TTS models
- Local speech recognition
- Sync when online

## Security Considerations

### API Key Management
- Secure storage of cloud service keys
- Key rotation strategies
- Environment-specific configurations
- Access control and permissions

### Privacy Protection
- Local processing preferences
- Data retention policies
- User consent management
- Anonymization strategies

### Audio Security
- Secure audio transmission
- Temporary file cleanup
- Recording permission management
- Audio data encryption
