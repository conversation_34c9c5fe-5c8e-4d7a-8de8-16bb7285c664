# Content Input Feature Documentation

## Overview

The Content Input feature allows users to import text, audio, images, and documents into the app for language learning purposes. This feature serves as the foundation for personalized practice content.

## Architecture

### Components

1. **ContentInputSelectionScreen**: Main selection interface
2. **Content Processors**: Handle different input types
3. **Text Recognition**: OCR and text extraction
4. **Audio Transcription**: Speech-to-text conversion
5. **Document Processing**: File parsing and text extraction

### Data Flow

```
User Input → Content Selection → Processing Service → AI Analysis → Database Storage → Practice Integration
```

## Input Methods

### 1. Text Input
**Direct text entry and document import**

**Supported Formats:**
- Plain text (.txt)
- Markdown (.md)
- Rich text documents
- PDF files (text extraction)

**Processing Pipeline:**
1. Text validation and cleaning
2. Language detection
3. AI-powered translation and analysis
4. Word and expression extraction
5. Database storage with metadata

**Implementation:**
```kotlin
// Document import screen
class DocumentImportScreen {
    fun onDocumentSelected(uri: Uri) {
        viewModel.processDocument(uri)
    }
}

// Document processing service
class DocumentTextProcessingService {
    suspend fun extractText(uri: Uri): String {
        // Extract text from various document formats
    }
}
```

### 2. Image Processing
**Camera capture and gallery selection with OCR**

**Features:**
- Real-time camera text detection
- Gallery image selection
- OCR text extraction using ML Kit
- Bounding box text selection
- Language detection

**Processing Pipeline:**
1. Image capture or selection
2. ML Kit text recognition
3. Text extraction and validation
4. Language detection
5. AI processing and translation
6. Storage with source image reference

**Implementation:**
```kotlin
// Live text recognition
class LiveTextRecognitionScreen {
    fun onTextBlockSelected(textBlock: VisionTextBlock) {
        // Process selected text block
    }
}

// Text recognition processor
class TextRecognitionProcessor {
    fun processImage(imageProxy: ImageProxy, onSuccess: (List<VisionTextBlock>) -> Unit) {
        // ML Kit text recognition
    }
}
```

### 3. Audio Processing
**Audio file import and transcription**

**Supported Formats:**
- MP3, M4A, AAC, WAV
- Voice recordings
- Imported audio files

**Processing Pipeline:**
1. Audio file validation
2. Audio metadata extraction
3. Audio segmentation (for large files)
4. Speech-to-text transcription
5. Text processing and translation
6. Storage with audio reference

**Implementation:**
```kotlin
// Audio transcription service
class AudioTranscriptionService {
    suspend fun transcribeAudio(audioUri: Uri): TranscriptionResult {
        // Convert audio to text
    }
}

// Audio file splitter
class AudioFileSplitterService {
    suspend fun splitAudio(audioUri: Uri, segmentDuration: Long): List<AudioSegment> {
        // Split large audio files
    }
}
```

### 4. Live Text Capture
**Real-time camera text detection and capture**

**Features:**
- Live camera preview
- Real-time text detection
- Interactive text selection
- Instant text processing
- Cropped image capture

**Implementation:**
```kotlin
// Live camera preview with text detection
@Composable
fun LiveCameraPreview(
    onImageCapture: (ImageProxy) -> Unit,
    captureTrigger: Int,
    onBitmapCaptured: (Bitmap) -> Unit
) {
    // CameraX integration with ML Kit
}
```

## Content Processing Services

### 1. AI Text Processing Service
**Firebase Gemini integration for content analysis**

**Capabilities:**
- Language detection
- Translation generation
- Word and expression extraction
- Pronunciation generation
- Context analysis

**Implementation:**
```kotlin
class FirebaseAITextProcessingService : AITextProcessingService {
    override suspend fun processText(
        text: String,
        sourceLanguage: String?,
        targetLanguage: String
    ): Translation {
        // Firebase Gemini API integration
    }
}
```

### 2. Language Detection
**Automatic source language identification**

**Features:**
- ML Kit language identification
- Confidence scoring
- Fallback detection methods
- Multi-language text handling

### 3. Content Validation
**Ensure content quality and appropriateness**

**Validation Steps:**
- Text length validation
- Language support verification
- Content appropriateness checking
- Duplicate content detection

## Data Models

### Translation Entity
```kotlin
@Entity(tableName = "translations")
data class Translation(
    @PrimaryKey val id: String,
    val originalText: String,
    val translatedText: String,
    val originalTextPronunciations: String?,
    val sourceLanguage: String,
    val targetLanguage: String,
    val timestamp: String,
    val source: String, // "image", "document", "audio", "text"
    val isStarred: Boolean,
    val words: List<WordDetail>,
    val expressions: List<ExpressionExample>
)
```

### Word Detail
```kotlin
data class WordDetail(
    val word: String,
    val normalizedWord: String,
    val pronunciation: String?,
    val translations: List<String>
)
```

### Expression Example
```kotlin
data class ExpressionExample(
    val sourceExpression: String,
    val translatedExpression: String,
    val normalizedExpression: String,
    val pronunciation: String?
)
```

## User Interface

### Content Selection Screen
**Main interface for choosing input method**

**Features:**
- Clear input method options
- Visual icons and descriptions
- Progress indicators
- Error handling and feedback

### Processing Screens
**Individual screens for each input type**

**Common Elements:**
- Progress indicators
- Cancel functionality
- Error handling
- Success feedback
- Navigation to results

### Results Validation
**Review and edit extracted content**

**Features:**
- Editable text fields
- Language selection
- Content preview
- Save/discard options
- Navigation to practice modes

## Error Handling

### Common Error Scenarios
1. **File Format Not Supported**
2. **Network Connection Issues**
3. **OCR Recognition Failures**
4. **Audio Transcription Errors**
5. **AI Processing Failures**

### Error Recovery Strategies
- Graceful degradation
- Retry mechanisms
- Alternative processing methods
- User guidance and suggestions
- Offline mode support

## Performance Considerations

### Image Processing
- Optimize image resolution for OCR
- Implement image compression
- Use background processing
- Cache processed results

### Audio Processing
- Stream large audio files
- Implement chunked processing
- Use efficient audio codecs
- Background transcription

### Memory Management
- Release resources promptly
- Use appropriate image loading libraries
- Implement proper lifecycle management
- Monitor memory usage

## Integration Points

### Practice Modes
Content input directly feeds into practice screens:
- Vocabulary practice with extracted words
- Pronunciation practice with audio content
- Reading comprehension with documents
- Conversation practice with content themes

### Progress Tracking
- Track content processing success rates
- Monitor user content preferences
- Analyze content difficulty levels
- Measure practice engagement

### Settings Integration
- Language preferences affect processing
- Quality settings for transcription
- Storage preferences for content
- Privacy settings for content handling

## Future Enhancements

### Advanced OCR
- Handwriting recognition
- Mathematical formula recognition
- Table and chart extraction
- Multi-column text handling

### Enhanced Audio Processing
- Speaker identification
- Noise reduction
- Multiple language detection
- Real-time transcription

### Smart Content Curation
- Content difficulty assessment
- Automatic content categorization
- Personalized content recommendations
- Content quality scoring

### Batch Processing
- Multiple file processing
- Folder import functionality
- Scheduled processing
- Background sync

## Testing Strategy

### Unit Tests
- Content processing services
- Data validation logic
- Error handling scenarios
- Model transformations

### Integration Tests
- End-to-end content processing
- Database operations
- API integrations
- File system operations

### UI Tests
- Screen navigation flows
- User input handling
- Error state displays
- Success feedback

## Security Considerations

### Data Privacy
- Secure content storage
- User consent for processing
- Data retention policies
- Content deletion capabilities

### API Security
- Secure API key management
- Request authentication
- Rate limiting
- Error information sanitization

### File Handling
- Validate file types and sizes
- Sanitize file names
- Secure temporary file handling
- Prevent malicious file uploads
