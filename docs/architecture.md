# Architecture Documentation

## Overview

The Language Practice App follows Clean Architecture principles with a feature-based modular structure. This document provides detailed information about the app's architecture, design patterns, and component relationships.

## Architecture Layers

### 1. Presentation Layer (UI)
- **Technology**: Jetpack Compose
- **Pattern**: MVVM (Model-View-ViewModel)
- **Components**: Screens, ViewModels, UI State classes

### 2. Domain Layer
- **Components**: Use cases, business logic, domain models
- **Responsibilities**: Core business rules, data transformations

### 3. Data Layer
- **Components**: Repositories, data sources, engines
- **Technologies**: Room, Firebase, ML Kit, Android APIs

## Project Structure

```
app/src/main/java/com/tfkcolin/practice/
├── core/                          # Core utilities and shared components
│   ├── InAppUpdateManager.kt      # App update management
│   └── ui/                        # Shared UI components
├── data/                          # Data layer components
│   ├── engine/                    # TTS/STT engine implementations
│   ├── model/                     # Database entities and models
│   └── preferences/               # User preferences management
├── di/                            # Dependency injection modules
├── features/                      # Feature modules
│   ├── auth/                      # Authentication feature
│   ├── audio/                     # Audio processing feature
│   ├── conversation/              # AI conversation feature
│   ├── content/                   # Content input selection
│   ├── home/                      # Home screen feature
│   ├── language/                  # Language management
│   ├── practice/                  # Practice modes
│   ├── settings/                  # Settings and configuration
│   ├── share/                     # Share functionality
│   ├── speech/                    # Speech recognition
│   ├── text/                      # Text processing and OCR
│   └── tts/                       # Text-to-speech
├── navigation/                    # Navigation setup
└── ui/                           # UI theme and components
```

## Feature Module Structure

Each feature follows a consistent structure:

```
feature/
├── data/                         # Data layer for the feature
│   ├── Repository.kt            # Data repository
│   ├── DataSource.kt           # Data sources (local/remote)
│   └── Models.kt               # Data models
├── domain/                      # Domain layer
│   ├── ViewModel.kt            # Presentation logic
│   ├── UiState.kt             # UI state definitions
│   └── UseCases.kt            # Business logic (if complex)
└── ui/                         # Presentation layer
    ├── Screen.kt              # Compose screens
    └── Components.kt          # Feature-specific components
```

## Key Architectural Components

### 1. Database Layer (Room)

**Entities:**
- `Translation`: Stores translated content with metadata
- `WordStats`: Tracks word encounter frequency
- `ExpressionStats`: Tracks expression encounter frequency

**DAOs:**
- `TranslationDao`: Translation CRUD operations
- `FrequencyStatsDao`: Statistics operations

### 2. Engine System

**TTS Engines:**
- `AndroidTtsEngine`: System TTS implementation
- `GoogleCloudTtsEngine`: Google Cloud TTS (planned)

**STT Engines:**
- `AndroidSpeechEngine`: System speech recognition

**Engine Management:**
- `TtsRepository`: TTS engine coordination
- `SpeechRepository`: STT engine coordination
- `LanguageManager`: Unified language and engine management

### 3. Content Processing Pipeline

**Text Processing:**
- `AITextProcessingService`: Firebase Gemini integration
- `DocumentTextProcessingService`: Document text extraction
- `TextRecognitionProcessor`: OCR processing

**Audio Processing:**
- `AudioTranscriptionService`: Audio-to-text conversion
- `AudioFileSplitterService`: Audio file segmentation
- `AudioMetadataService`: Audio file analysis

**Image Processing:**
- ML Kit Text Recognition
- CameraX integration
- Real-time text detection

### 4. Authentication System

**Components:**
- `AuthRepository`: Firebase Auth integration
- `AuthViewModel`: Authentication state management
- Google Sign-In with Credential Manager API

### 5. Navigation System

**Structure:**
- Type-safe navigation with sealed classes
- Route definitions in `Route.kt`
- Centralized navigation graph in `AppNavGraph.kt`

## Design Patterns

### 1. Repository Pattern
- Abstracts data sources from ViewModels
- Provides clean API for data operations
- Handles data source coordination

### 2. Observer Pattern
- StateFlow for reactive UI updates
- Flow for data stream management
- Lifecycle-aware data observation

### 3. Dependency Injection
- Hilt for compile-time DI
- Module-based dependency organization
- Scoped dependencies for proper lifecycle management

### 4. State Management
- Unidirectional data flow
- Immutable UI state classes
- Event-driven state updates

## Data Flow

### 1. Content Import Flow
```
User Input → Content Selection → Processing Service → AI Analysis → Database Storage → UI Update
```

### 2. Practice Flow
```
User Action → ViewModel → Engine Repository → TTS/STT Engine → Audio Processing → UI Feedback
```

### 3. Translation Flow
```
Text Input → Language Detection → AI Processing → Translation Generation → Database Storage → UI Display
```

## Error Handling Strategy

### 1. Layered Error Handling
- UI Layer: User-friendly error messages
- Domain Layer: Business logic validation
- Data Layer: Network and database error handling

### 2. Error Types
- Network errors with retry mechanisms
- Validation errors with user guidance
- System errors with fallback options

### 3. Error Recovery
- Graceful degradation for optional features
- Offline mode support where applicable
- User-initiated retry mechanisms

## Performance Considerations

### 1. Database Optimization
- Indexed queries for frequent operations
- Pagination for large datasets
- Background processing for heavy operations

### 2. Memory Management
- Proper lifecycle management for engines
- Image processing optimization
- Coroutine scope management

### 3. Network Optimization
- Request caching where appropriate
- Efficient API usage patterns
- Background sync strategies

## Security Considerations

### 1. Authentication
- Firebase Auth for secure user management
- Token-based authentication
- Secure credential storage

### 2. Data Protection
- Local database encryption (if needed)
- Secure API communication
- User data privacy compliance

### 3. Permissions
- Runtime permission handling
- Minimal permission requests
- Clear permission explanations

## Testing Strategy

### 1. Unit Testing
- ViewModel testing with test coroutines
- Repository testing with mock data sources
- Business logic validation

### 2. Integration Testing
- Database operations
- API integrations
- Engine functionality

### 3. UI Testing
- Compose UI testing
- Navigation testing
- User interaction flows

## Future Architecture Considerations

### 1. Modularization
- Feature modules as separate Gradle modules
- Shared core modules
- Dynamic feature delivery

### 2. Scalability
- Multi-user support
- Cloud synchronization
- Advanced analytics

### 3. Platform Expansion
- Shared business logic for multi-platform
- Platform-specific implementations
- Cross-platform data synchronization
